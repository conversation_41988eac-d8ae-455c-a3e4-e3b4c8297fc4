package com.nnnmkj.thai.module.course.service.assignment;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDTO;
import jakarta.validation.Valid;

/**
 * 课程作业 Service 接口
 *
 * <AUTHOR>
 */
public interface AssignmentService {

    /**
     * 创建课程作业
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAssignment(@Valid AssignmentSaveReqVO createReqVO);

    /**
     * 更新课程作业
     *
     * @param updateReqVO 更新信息
     */
    void updateAssignment(@Valid AssignmentSaveReqVO updateReqVO);

    /**
     * 删除课程作业
     *
     * @param id 编号
     */
    void deleteAssignment(Long id);

    /**
     * 获得课程作业
     *
     * @param id 编号
     * @return 课程作业
     */
    AssignmentDO getAssignment(Long id);

    /**
     * 获得课程作业分页
     *
     * @param pageReqVO 分页查询
     * @return 课程作业分页
     */
    PageResult<AssignmentDTO> getAssignmentPage(AssignmentPageReqVO pageReqVO);

    /**
     * 获得课程作业分页
     *
     * @param pageReqVO 分页查询
     * @return 课程作业分页
     */
    PageResult<AssignmentDO> getAssignmentPage(AppAssignmentPageReqVO pageReqVO);

    /**
     * 创建课程作业
     *
     * @param userId 用户编号
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAssignment(Long userId, @Valid AppAssignmentSaveReqVO createReqVO);

    /**
     * 更新课程作业
     *
     * @param userId 用户编号
     * @param updateReqVO 更新信息
     */
    void updateAssignment(Long userId, @Valid AppAssignmentSaveReqVO updateReqVO);

    /**
     * 删除课程作业
     *
     * @param userId 用户编号
     * @param id 编号
     */
    void deleteAssignment(Long userId, Long id);
}