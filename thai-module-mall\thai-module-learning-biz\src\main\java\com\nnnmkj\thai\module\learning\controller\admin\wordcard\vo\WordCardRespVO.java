package com.nnnmkj.thai.module.learning.controller.admin.wordcard.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 单词卡 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WordCardRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2435")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "学习集编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15960")
    @ExcelProperty("学习集编号")
    private Long wordSetId;

    @Schema(description = "词语", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("词语")
    private String word;

    @Schema(description = "音标")
    @ExcelProperty("音标")
    private String phoneticSymbol;

    @Schema(description = "图片", example = "https://www.iocoder.cn")
    @ExcelProperty("图片")
    private String imageUrl;

    @Schema(description = "音频", example = "https://www.iocoder.cn")
    @ExcelProperty("音频")
    private String audioUrl;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}