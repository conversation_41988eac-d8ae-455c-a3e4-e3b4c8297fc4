package com.nnnmkj.thai.module.learning.controller.admin.sessionresult;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDTO;
import com.nnnmkj.thai.module.learning.service.sessionresult.SessionResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会话成绩")
@RestController
@RequestMapping("/learning/session-result")
@Validated
public class SessionResultController {

    @Resource
    private SessionResultService sessionResultService;

    @PostMapping("/create")
    @Operation(summary = "创建会话成绩")
    @PreAuthorize("@ss.hasPermission('learning:session-result:create')")
    public CommonResult<Long> createSessionResult(@Valid @RequestBody SessionResultSaveReqVO createReqVO) {
        return success(sessionResultService.createSessionResult(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会话成绩")
    @PreAuthorize("@ss.hasPermission('learning:session-result:update')")
    public CommonResult<Boolean> updateSessionResult(@Valid @RequestBody SessionResultSaveReqVO updateReqVO) {
        sessionResultService.updateSessionResult(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除会话成绩")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:session-result:delete')")
    public CommonResult<Boolean> deleteSessionResult(@RequestParam("id") Long id) {
        sessionResultService.deleteSessionResult(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会话成绩")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:session-result:query')")
    public CommonResult<SessionResultRespVO> getSessionResult(@RequestParam("id") Long id) {
        SessionResultDO sessionResult = sessionResultService.getSessionResult(id);
        return success(BeanUtils.toBean(sessionResult, SessionResultRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会话成绩分页")
    @PreAuthorize("@ss.hasPermission('learning:session-result:query')")
    public CommonResult<PageResult<SessionResultRespVO>> getSessionResultPage(@Valid SessionResultPageReqVO pageReqVO) {
        PageResult<SessionResultDTO> pageResult = sessionResultService.getSessionResultPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, SessionResultRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会话成绩 Excel")
    @PreAuthorize("@ss.hasPermission('learning:session-result:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSessionResultExcel(@Valid SessionResultPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SessionResultDTO> list = sessionResultService.getSessionResultPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会话成绩.xls", "数据", SessionResultRespVO.class,
                        BeanUtils.toBean(list, SessionResultRespVO.class));
    }

}