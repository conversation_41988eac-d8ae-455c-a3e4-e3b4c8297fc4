package com.nnnmkj.thai.module.course.service.assignment;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDTO;
import com.nnnmkj.thai.module.course.dal.mysql.assignment.AssignmentMapper;
import com.nnnmkj.thai.module.course.dal.mysql.assignment.AssignmentMapperX;
import com.nnnmkj.thai.module.course.service.assignmentquestion.AssignmentQuestionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.ASSIGNMENT_NOT_EXISTS;

/**
 * 课程作业 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssignmentServiceImpl implements AssignmentService {

    @Resource
    private AssignmentMapper assignmentMapper;

    @Resource
    private AssignmentMapperX assignmentMapperX;
    @Resource
    private AssignmentQuestionService assignmentQuestionService;

    // ==================== 管理后台 ====================

    @Override
    public Long createAssignment(AssignmentSaveReqVO createReqVO) {
        // 插入
        AssignmentDO assignment = BeanUtils.toBean(createReqVO, AssignmentDO.class);
        assignmentMapper.insert(assignment);
        // 返回
        return assignment.getId();
    }

    @Override
    public void updateAssignment(AssignmentSaveReqVO updateReqVO) {
        // 校验存在
        validateAssignmentExists(updateReqVO.getId());
        // 更新
        AssignmentDO updateObj = BeanUtils.toBean(updateReqVO, AssignmentDO.class);
        assignmentMapper.updateById(updateObj);
    }

    @Override
    public void deleteAssignment(Long id) {
        // 校验存在
        validateAssignmentExists(id);
        // 删除作业
        assignmentMapper.deleteById(id);
        // 删除题目及选项
        assignmentQuestionService.deleteAssignmentQuestionByAssignmentId(id);
    }

    private void validateAssignmentExists(Long id) {
        if (assignmentMapper.selectById(id) == null) {
            throw exception(ASSIGNMENT_NOT_EXISTS);
        }
    }

    @Override
    public AssignmentDO getAssignment(Long id) {
        return assignmentMapper.selectById(id);
    }

    @Override
    public PageResult<AssignmentDTO> getAssignmentPage(AssignmentPageReqVO pageReqVO) {
        return assignmentMapperX.selectPage(pageReqVO);
    }

    // ==================== 用户 App ====================

    @Override
    public PageResult<AssignmentDO> getAssignmentPage(AppAssignmentPageReqVO pageReqVO) {
        return assignmentMapper.selectPage(pageReqVO);
    }

    @Override
    public Long createAssignment(Long userId, AppAssignmentSaveReqVO createReqVO) {
        // 插入
        AssignmentDO assignment = BeanUtils.toBean(createReqVO, AssignmentDO.class);
        assignment.setUserId(userId);
        assignmentMapper.insert(assignment);
        // 返回
        return assignment.getId();
    }

    @Override
    public void updateAssignment(Long userId, AppAssignmentSaveReqVO updateReqVO) {
        // 校验存在
        validateAssignmentExists(userId, updateReqVO.getId());
        // 更新
        AssignmentDO updateObj = BeanUtils.toBean(updateReqVO, AssignmentDO.class);
        assignmentMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAssignment(Long userId, Long id) {
        // 校验存在
        validateAssignmentExists(userId, id);
        // 删除作业
        assignmentMapper.deleteById(id);
        // 删除题目及选项
        assignmentQuestionService.deleteAssignmentQuestionByAssignmentId(id);
    }

    private void validateAssignmentExists(Long userId, Long id) {
        AssignmentDO exists = assignmentMapper.selectOne(AssignmentDO::getId, id, AssignmentDO::getUserId, userId);
        if (exists == null) {
            throw exception(ASSIGNMENT_NOT_EXISTS);
        }
    }

}