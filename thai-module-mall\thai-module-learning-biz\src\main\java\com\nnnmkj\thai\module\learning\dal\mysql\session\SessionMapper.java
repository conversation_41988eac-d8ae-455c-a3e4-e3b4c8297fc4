package com.nnnmkj.thai.module.learning.dal.mysql.session;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.app.session.vo.AppSessionPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习会话 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionMapper extends BaseMapperX<SessionDO> {

    default PageResult<SessionDO> selectPage(SessionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<SessionDO>()
                .eqIfPresent(SessionDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionDO::getCreator, creatorQuery)
                .eqIfPresent(SessionDO::getMode, reqVO.getMode())
                .eqIfPresent(SessionDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SessionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionDO::getId));
    }

    default PageResult<SessionDO> selectAppPage(AppSessionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<SessionDO>()
                .eqIfPresent(SessionDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionDO::getCreator, creatorQuery)
                .eqIfPresent(SessionDO::getMode, reqVO.getMode())
                .eqIfPresent(SessionDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SessionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionDO::getId));
    }

}