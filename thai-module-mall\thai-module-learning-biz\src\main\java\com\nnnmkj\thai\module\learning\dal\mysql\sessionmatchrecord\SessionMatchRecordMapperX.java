package com.nnnmkj.thai.module.learning.dal.mysql.sessionmatchrecord;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 配对记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionMatchRecordMapperX extends BaseMapperX<SessionMatchRecordDTO> {

    default PageResult<SessionMatchRecordDTO> selectPage(SessionMatchRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<SessionMatchRecordDTO> wrapper = new MPJLambdaWrapperX<SessionMatchRecordDTO>()
                .selectAll(SessionMatchRecordDO.class)
                .selectAs("t1.nickname", SessionMatchRecordDTO::getNickname)
                .selectAs(WordSetDO::getTitle, SessionMatchRecordDTO::getSetTitle)
                .eqIfPresent(SessionMatchRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionMatchRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionMatchRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionMatchRecordDO::getWord, reqVO.getWord())
                .eqIfPresent(SessionMatchRecordDO::getDefinition, reqVO.getDefinition())
                .eqIfPresent(SessionMatchRecordDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(SessionMatchRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionMatchRecordDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, SessionMatchRecordDO::getSetId);
        return selectPage(reqVO, wrapper);
    }
    

}