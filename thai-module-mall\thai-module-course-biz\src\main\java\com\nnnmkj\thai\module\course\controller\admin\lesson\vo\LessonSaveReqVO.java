package com.nnnmkj.thai.module.course.controller.admin.lesson.vo;

import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonAttachmentDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 课程新增/修改 Request VO")
@Data
public class LessonSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6445")
    private Long id;

    @Schema(description = "课程标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "课程标题不能为空")
    private String title;

    @Schema(description = "课程简介", example = "你猜")
    private String description;

    @Schema(description = "课程封面图URL")
    private String coverImage;

    @Schema(description = "课程附件列表")
    private List<LessonAttachmentDO> lessonAttachments;

}