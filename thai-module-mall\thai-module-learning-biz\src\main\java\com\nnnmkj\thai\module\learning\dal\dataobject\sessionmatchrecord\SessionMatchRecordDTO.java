package com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord;

import lombok.*;

/**
 * 配对记录 DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class SessionMatchRecordDTO extends SessionMatchRecordDO {


    /**
     * 学习集标题
     */
    private String setTitle;
    /**
     * 用户昵称
     */
    private String nickname;

}