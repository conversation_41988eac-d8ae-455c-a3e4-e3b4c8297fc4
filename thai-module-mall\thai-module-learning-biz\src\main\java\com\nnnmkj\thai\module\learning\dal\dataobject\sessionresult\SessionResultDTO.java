package com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult;

import lombok.*;

/**
 * 会话成绩 DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class SessionResultDTO extends SessionResultDO {

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 学习集标题
     */
    private String setTitle;
}