package com.nnnmkj.thai.module.quiz.service.questionBank;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo.*;
import com.nnnmkj.thai.module.quiz.controller.app.questionBank.vo.AppQuestionBankPageReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankKnowledgePoint.QuestionBankKnowledgePointDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankOption.QuestionBankOptionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankTag.QuestionBankTagDO;
import com.nnnmkj.thai.module.quiz.dal.mysql.questionBank.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.quiz.enums.ErrorCodeConstants.QUESTION_BANK_NOT_EXISTS;

/**
 * 题目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionBankServiceImpl implements QuestionBankService {

    @Resource
    private QuestionBankMapper questionBankMapper;
    @Resource
    private QuestionBankMapperX questionBankMapperX;
    @Resource
    private QuestionBankKnowledgePointMapper questionBankKnowledgePointMapper;
    @Resource
    private QuestionBankOptionMapper questionBankOptionMapper;
    @Resource
    private QuestionBankTagMapper questionBankTagMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createQuestionBank(QuestionBankSaveReqVO createReqVO) {
        // 插入
        QuestionBankDO questionBank = BeanUtils.toBean(createReqVO, QuestionBankDO.class);
        questionBankMapper.insert(questionBank);

        // 插入子表
        createQuestionBankKnowledgePointList(questionBank.getId(), createReqVO.getQuestionBankKnowledgePoints());
        createQuestionBankOptionList(questionBank.getId(), createReqVO.getQuestionBankOptions());
        createQuestionBankTagList(questionBank.getId(), createReqVO.getQuestionBankTags());
        // 返回
        return questionBank.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuestionBank(QuestionBankSaveReqVO updateReqVO) {
        // 校验存在
        validateQuestionBankExists(updateReqVO.getId());
        // 更新
        QuestionBankDO updateObj = BeanUtils.toBean(updateReqVO, QuestionBankDO.class);
        questionBankMapper.updateById(updateObj);

        // 更新子表
        updateQuestionBankKnowledgePointList(updateReqVO.getId(), updateReqVO.getQuestionBankKnowledgePoints());
        updateQuestionBankOptionList(updateReqVO.getId(), updateReqVO.getQuestionBankOptions());
        updateQuestionBankTagList(updateReqVO.getId(), updateReqVO.getQuestionBankTags());
    }

    @Override
    public void auditQuestionBank(QuestionBankAuditReqVO updateReqVO) {
        QuestionBankDO questionBankDO = questionBankMapper.selectById(updateReqVO.getId());
        // 校验存在
        if (questionBankDO == null) {
            throw exception(QUESTION_BANK_NOT_EXISTS);
        }
        // 更新
        BeanUtils.copyProperties(updateReqVO, questionBankDO);
        questionBankMapper.updateById(questionBankDO);
    }

    @Override
    public void batchAuditQuestionBank(QuestionBankBatchAuditReqVO reqVO) {
        List<Long> ids = reqVO.getIds();
        Integer auditStatus = reqVO.getAuditStatus();
        String rejectReason = reqVO.getRejectReason();
        List<QuestionBankDO> bankDOList = questionBankMapper.selectList(new LambdaQueryWrapperX<QuestionBankDO>().in(QuestionBankDO::getId, ids));
        for (QuestionBankDO questionBankDO : bankDOList) {
            questionBankDO.setAuditStatus(auditStatus);
            questionBankDO.setRejectReason(rejectReason);
        }
        questionBankMapper.updateBatch(bankDOList);
    }

    @Override
    public void batchAddTag(QuestionBankBatchAddTagReqVO reqVO) {
        List<Long> ids = reqVO.getIds();
        String tag = reqVO.getTag();
        List<QuestionBankTagDO> list = new ArrayList<>();
        for (Long id : ids) {
            list.add(QuestionBankTagDO.builder().questionId(id).content(tag).build());
        }
        questionBankTagMapper.insertBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteQuestionBank(Long id) {
        // 校验存在
        validateQuestionBankExists(id);
        // 删除
        questionBankMapper.deleteById(id);

        // 删除子表
        deleteQuestionBankKnowledgePointByQuestionId(id);
        deleteQuestionBankOptionByQuestionId(id);
        deleteQuestionBankTagByQuestionId(id);
    }

    private void validateQuestionBankExists(Long id) {
        if (questionBankMapper.selectById(id) == null) {
            throw exception(QUESTION_BANK_NOT_EXISTS);
        }
    }

    @Override
    public QuestionBankDTO getQuestionBank(Long id) {
        return questionBankMapperX.selectById(id);
    }

    @Override
    public PageResult<QuestionBankDTO> getQuestionBankPage(QuestionBankPageReqVO pageReqVO) {
        return questionBankMapperX.selectPage(pageReqVO);
    }

    @Override
    public PageResult<QuestionBankDTO> getQuestionBankPage(AppQuestionBankPageReqVO pageReqVO) {
        return questionBankMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<QuestionBankDO> getQuestionBankList(List<Long> ids) {
        return questionBankMapper.selectByIds(ids);
    }

    // ==================== 子表（题目知识点） ====================

    @Override
    public List<QuestionBankKnowledgePointDO> getQuestionBankKnowledgePointListByQuestionId(Long questionId) {
        return questionBankKnowledgePointMapper.selectListByQuestionId(questionId);
    }

    @Override
    public List<QuestionBankKnowledgePointDO> getQuestionBankKnowledgePointListByQuestionIds(List<Long> questionIds) {
        return questionBankKnowledgePointMapper.selectListByQuestionIds(questionIds);
    }

    private void createQuestionBankKnowledgePointList(Long questionId, List<QuestionBankKnowledgePointDO> list) {
        list.forEach(o -> o.setQuestionId(questionId));
        questionBankKnowledgePointMapper.insertBatch(list);
    }

    private void updateQuestionBankKnowledgePointList(Long questionId, List<QuestionBankKnowledgePointDO> list) {
        deleteQuestionBankKnowledgePointByQuestionId(questionId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createQuestionBankKnowledgePointList(questionId, list);
    }

    private void deleteQuestionBankKnowledgePointByQuestionId(Long questionId) {
        questionBankKnowledgePointMapper.deleteByQuestionId(questionId);
    }

    // ==================== 子表（题目选项） ====================

    @Override
    public List<QuestionBankOptionDO> getQuestionBankOptionListByQuestionId(Long questionId) {
        return questionBankOptionMapper.selectListByQuestionId(questionId);
    }

    @Override
    public List<QuestionBankOptionDO> getQuestionBankOptionListByQuestionIds(List<Long> questionIds) {
        return questionBankOptionMapper.selectListByQuestionIds(questionIds);
    }

    private void createQuestionBankOptionList(Long questionId, List<QuestionBankOptionDO> list) {
        list.forEach(o -> o.setQuestionId(questionId));
        questionBankOptionMapper.insertBatch(list);
    }

    private void updateQuestionBankOptionList(Long questionId, List<QuestionBankOptionDO> list) {
        deleteQuestionBankOptionByQuestionId(questionId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createQuestionBankOptionList(questionId, list);
    }

    private void deleteQuestionBankOptionByQuestionId(Long questionId) {
        questionBankOptionMapper.deleteByQuestionId(questionId);
    }

    // ==================== 子表（题目标签） ====================

    @Override
    public List<QuestionBankTagDO> getQuestionBankTagListByQuestionId(Long questionId) {
        return questionBankTagMapper.selectListByQuestionId(questionId);
    }

    @Override
    public List<QuestionBankTagDO> getQuestionBankTagListByQuestionIds(List<Long> questionIds) {
        return questionBankTagMapper.selectListByQuestionIds(questionIds);
    }

    private void createQuestionBankTagList(Long questionId, List<QuestionBankTagDO> list) {
        list.forEach(o -> o.setQuestionId(questionId));
        questionBankTagMapper.insertBatch(list);
    }

    private void updateQuestionBankTagList(Long questionId, List<QuestionBankTagDO> list) {
        deleteQuestionBankTagByQuestionId(questionId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createQuestionBankTagList(questionId, list);
    }

    private void deleteQuestionBankTagByQuestionId(Long questionId) {
        questionBankTagMapper.deleteByQuestionId(questionId);
    }

}