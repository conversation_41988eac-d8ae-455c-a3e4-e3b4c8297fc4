package com.nnnmkj.thai.module.course.service.assignmentrelease;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.AssignmentReleasePageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.AssignmentReleaseCreateReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.AssignmentReleaseUpdateReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentrelease.vo.*;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentrelease.AssignmentReleaseDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 课程作业发布 Service 接口
 *
 * <AUTHOR>
 */
public interface AssignmentReleaseService {

    /**
     * 创建课程作业发布
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAssignmentRelease(@Valid AssignmentReleaseCreateReqVO createReqVO);

    /**
     * 更新课程作业发布
     *
     * @param updateReqVO 更新信息
     */
    void updateAssignmentRelease(@Valid AssignmentReleaseUpdateReqVO updateReqVO);

    /**
     * 删除课程作业发布
     *
     * @param id 编号
     */
    void deleteAssignmentRelease(Long id);

    /**
     * 获得课程作业发布
     *
     * @param id 编号
     * @return 课程作业发布
     */
    AssignmentReleaseDO getAssignmentRelease(Long id);

    /**
     * 获得课程作业发布详情
     *
     * @param id 编号
     * @param userId 用户编号
     * @return 课程作业发布
     */
    AppAssignmentReleaseDetailRespVO getAssignmentReleaseDetail(Long id, Long userId);

    /**
     * 获得课程作业发布分页
     *
     * @param pageReqVO 分页查询
     * @return 课程作业发布分页
     */
    PageResult<AssignmentReleaseDO> getAssignmentReleaseVOPage(AssignmentReleasePageReqVO pageReqVO);

    /**
     * 获得课程作业发布VO分页
     *
     * @param pageReqVO 分页查询
     * @return 课程作业发布分页
     */
    PageResult<AppAssignmentReleaseRespVO> getAssignmentReleaseVOPage(AppAssignmentReleasePageReqVO pageReqVO);

    /**
     * 获取用户的课程作业发布分页
     *
     * @param userId    用户ID
     * @param groupId   班级ID
     * @param pageReqVO 分页查询
     * @return 课程作业发布分页
     */
    PageResult<AppAssignmentReleaseSimpleRespVO> getAssignmentReleaseSimpleVOPage(Long userId, Long groupId, @Valid AppAssignmentReleasePageReqVO pageReqVO);

    /**
     * 提交答题
     *
     * @param userId 用户编号
     * @param reqVO  提交请求参数
     */
    void answerSubmitAssignmentRelease(Long userId, @Valid AppAssignmentReleaseAnswerSubmitReqVO reqVO);

    /**
     * 获取答题报告
     *
     * @param id  课程作业发布编号
     * @param userId 用户编号
     * @return 答题报告
     */
    AppAssignmentReleaseAnswerReportRespVO answerReportAssignmentRelease(@NotNull Long id, Long userId);

    /**
     * 获取答题统计报告
     *
     * @param assignmentReleaseId 课程作业发布编号
     * @param groupId 分组编号
     * @param userId 用户编号
     * @return 答题统计报告
     */
    AppAssignmentReleaseAnswerStatisticsRespVO statisticsReportAssignmentRelease(@NotNull Long userId, @NotNull Long groupId, @NotNull Long assignmentReleaseId);

    /**
     * 删除课程作业发布
     *
     * @param id 编号
     * @param userId 用户编号
     */
    void deleteAssignmentRelease(Long id, Long userId);

}