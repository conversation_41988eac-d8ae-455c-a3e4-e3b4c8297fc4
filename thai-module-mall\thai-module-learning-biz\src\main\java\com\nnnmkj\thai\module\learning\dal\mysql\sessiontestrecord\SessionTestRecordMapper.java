package com.nnnmkj.thai.module.learning.dal.mysql.sessiontestrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测试记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionTestRecordMapper extends BaseMapperX<SessionTestRecordDO> {

    default PageResult<SessionTestRecordDO> selectPage(SessionTestRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<SessionTestRecordDO>()
                .eqIfPresent(SessionTestRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionTestRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionTestRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionTestRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(SessionTestRecordDO::getUserAnswer, reqVO.getUserAnswer())
                .eqIfPresent(SessionTestRecordDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(SessionTestRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionTestRecordDO::getId));
    }

}