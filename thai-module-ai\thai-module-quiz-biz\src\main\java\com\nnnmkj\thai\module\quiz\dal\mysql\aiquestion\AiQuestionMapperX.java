package com.nnnmkj.thai.module.quiz.dal.mysql.aiquestion;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionPageReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI出题 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiQuestionMapperX extends BaseMapperX<AiQuestionDTO> {

    default PageResult<AiQuestionDTO> selectPage(AiQuestionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<AiQuestionDTO> wrapper = new MPJLambdaWrapperX<AiQuestionDTO>()
                .selectAll(AiQuestionDO.class)
                .selectAs("t1.nickname", AiQuestionDTO::getNickname)
                .eqIfPresent(AiQuestionDO::getUserId, reqVO.getUserId())
                .eqIfPresent(AiQuestionDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AiQuestionDO::getCurrentNode, reqVO.getCurrentNode())
                .eqIfPresent(AiQuestionDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AiQuestionDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(AiQuestionDO::getCreator, creatorQuery)
                .orderByDesc(AiQuestionDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)");
        return selectPage(reqVO, wrapper);
    }

}