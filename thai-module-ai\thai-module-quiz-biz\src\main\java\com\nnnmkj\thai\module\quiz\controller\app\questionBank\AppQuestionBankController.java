package com.nnnmkj.thai.module.quiz.controller.app.questionBank;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.quiz.controller.app.questionBank.vo.AppQuestionBankPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.app.questionBank.vo.AppQuestionBankRespVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankKnowledgePoint.QuestionBankKnowledgePointDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankOption.QuestionBankOptionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankTag.QuestionBankTagDO;
import com.nnnmkj.thai.module.quiz.enums.AuditStatusEnum;
import com.nnnmkj.thai.module.quiz.service.questionBank.QuestionBankService;
import com.nnnmkj.thai.module.quiz.utils.questionbank.SourceTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户APP - 题库")
@RestController
@RequestMapping("/quiz/question-bank")
@Validated
public class AppQuestionBankController {

    @Resource
    private QuestionBankService questionBankService;

    @GetMapping("/get")
    @Operation(summary = "获得题目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppQuestionBankRespVO> getQuestionBank(@RequestParam("id") Long id) {
        QuestionBankDTO questionBank = questionBankService.getQuestionBank(id);
        AppQuestionBankRespVO respVO = BeanUtils.toBean(questionBank, AppQuestionBankRespVO.class);
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得题目分页")
    public CommonResult<PageResult<AppQuestionBankRespVO>> getQuestionBankPage(@Valid AppQuestionBankPageReqVO pageReqVO) {
        // 查询审核通过数据
        pageReqVO.setAuditStatus(AuditStatusEnum.PASS.getType());

        // 根据来源类型设置过滤条件
        Integer sourceType = pageReqVO.getSourceType();
        SourceTypeEnum sourceTypeEnum = SourceTypeEnum.ALL;
        if (Objects.nonNull(sourceType)) {
            sourceTypeEnum = SourceTypeEnum.valueOf(pageReqVO.getSourceType());
        }
        switch (sourceTypeEnum) {
            case PUBLIC -> { // 公开
                pageReqVO.setPublicStatus(Boolean.TRUE);
                pageReqVO.setUserId(null);
            }
            case MYSELF -> { // 本人
                pageReqVO.setPublicStatus(null);
                pageReqVO.setUserId(getLoginUserId());
            }
            default -> { // 全选
                pageReqVO.setPublicStatus(Boolean.TRUE);
                pageReqVO.setUserId(getLoginUserId());
            }
        }

        // 查询数据
        pageReqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        PageResult<QuestionBankDTO> pageResult = questionBankService.getQuestionBankPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        PageResult<AppQuestionBankRespVO> result = BeanUtils.toBean(pageResult, AppQuestionBankRespVO.class);
        List<Long> ids = pageResult.getList().stream().map(QuestionBankDO::getId).toList();

        List<QuestionBankKnowledgePointDO> KnowledgePoints = questionBankService.getQuestionBankKnowledgePointListByQuestionIds(ids);
        if (CollUtil.isNotEmpty(KnowledgePoints)) {
            result.getList().forEach(o -> o.setQuestionBankKnowledgePoints(KnowledgePoints.stream().filter(k -> k.getQuestionId().equals(o.getId())).toList()));
        }
        List<QuestionBankOptionDO> questionBankOptions = questionBankService.getQuestionBankOptionListByQuestionIds(ids);
        if (CollUtil.isNotEmpty(questionBankOptions)) {
            result.getList().forEach(o -> o.setQuestionBankOptions(questionBankOptions.stream().filter(k -> k.getQuestionId().equals(o.getId())).toList()));
        }

        List<QuestionBankTagDO> questionBankTags = questionBankService.getQuestionBankTagListByQuestionIds(ids);
        if (CollUtil.isNotEmpty(questionBankTags)) {
            result.getList().forEach(o -> o.setQuestionBankTags(questionBankTags.stream().filter(k -> k.getQuestionId().equals(o.getId())).toList()));
        }
        return success(result);
    }

}