package com.nnnmkj.thai.module.learning.dal.mysql.sessionstudyrecord;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionStudyRecordMapperX extends BaseMapperX<SessionStudyRecordDTO> {

    default PageResult<SessionStudyRecordDTO> selectPage(SessionStudyRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<SessionStudyRecordDTO> wrapper = new MPJLambdaWrapperX<SessionStudyRecordDTO>()
                .selectAll(SessionStudyRecordDO.class)
                .selectAs("t1.nickname", SessionStudyRecordDTO::getNickname)
                .selectAs(WordSetDO::getTitle, SessionStudyRecordDTO::getSetTitle)
                .eqIfPresent(SessionStudyRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionStudyRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionStudyRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionStudyRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(SessionStudyRecordDO::getUserAnswer, reqVO.getUserAnswer())
                .eqIfPresent(SessionStudyRecordDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(SessionStudyRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionStudyRecordDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, SessionStudyRecordDO::getSetId);
        return selectPage(reqVO, wrapper);
    }

}