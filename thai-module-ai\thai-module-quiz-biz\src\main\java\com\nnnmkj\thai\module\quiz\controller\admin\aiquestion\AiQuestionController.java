package com.nnnmkj.thai.module.quiz.controller.admin.aiquestion;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionRespVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionSaveReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionSimpleRespVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDTO;
import com.nnnmkj.thai.module.quiz.service.aiquestion.AiQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI出题")
@RestController
@RequestMapping("/quiz/ai-question")
@Validated
public class AiQuestionController {

    @Resource
    private AiQuestionService aiQuestionService;

    @PostMapping("/create")
    @Operation(summary = "创建AI出题")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question:create')")
    public CommonResult<Long> createAiQuestion(@Valid @RequestBody AiQuestionSaveReqVO createReqVO) {
        return success(aiQuestionService.createAiQuestion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新AI出题")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question:update')")
    public CommonResult<Boolean> updateAiQuestion(@Valid @RequestBody AiQuestionSaveReqVO updateReqVO) {
        aiQuestionService.updateAiQuestion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除AI出题")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('quiz:ai-question:delete')")
    public CommonResult<Boolean> deleteAiQuestion(@RequestParam("id") Long id) {
        aiQuestionService.deleteAiQuestion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI出题")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question:query')")
    public CommonResult<AiQuestionRespVO> getAiQuestion(@RequestParam("id") Long id) {
        AiQuestionDO aiQuestion = aiQuestionService.getAiQuestion(id);
        return success(BeanUtils.toBean(aiQuestion, AiQuestionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得AI出题分页")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question:query')")
    public CommonResult<PageResult<AiQuestionRespVO>> getAiQuestionPage(@Valid AiQuestionPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<AiQuestionDTO> pageResult = aiQuestionService.getAiQuestionPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, AiQuestionRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得AI出题简易列表")
    public CommonResult<List<AiQuestionSimpleRespVO>> getWordSetSimpleList(@Valid AiQuestionPageReqVO reqVO) {
        if (reqVO.getUserType() == null) {
            reqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        List<AiQuestionDO> listResult = aiQuestionService.getAiQuestionList(reqVO);
        if (CollUtil.isEmpty(listResult)) {
            return success(Collections.emptyList());
        }
        return success(BeanUtils.toBean(listResult, AiQuestionSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出AI出题 Excel")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAiQuestionExcel(@Valid AiQuestionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        List<AiQuestionDTO> list = aiQuestionService.getAiQuestionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI出题.xls", "数据", AiQuestionRespVO.class,
                        BeanUtils.toBean(list, AiQuestionRespVO.class));
    }

}