package com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDTO;
import com.nnnmkj.thai.module.learning.service.sessiontestrecord.SessionTestRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测试记录")
@RestController
@RequestMapping("/learning/session-test-record")
@Validated
public class SessionTestRecordController {

    @Resource
    private SessionTestRecordService sessionTestRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建测试记录")
    @PreAuthorize("@ss.hasPermission('learning:session-test-record:create')")
    public CommonResult<Long> createSessionTestRecord(@Valid @RequestBody SessionTestRecordSaveReqVO createReqVO) {
        return success(sessionTestRecordService.createSessionTestRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新测试记录")
    @PreAuthorize("@ss.hasPermission('learning:session-test-record:update')")
    public CommonResult<Boolean> updateSessionTestRecord(@Valid @RequestBody SessionTestRecordSaveReqVO updateReqVO) {
        sessionTestRecordService.updateSessionTestRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测试记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:session-test-record:delete')")
    public CommonResult<Boolean> deleteSessionTestRecord(@RequestParam("id") Long id) {
        sessionTestRecordService.deleteSessionTestRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测试记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:session-test-record:query')")
    public CommonResult<SessionTestRecordRespVO> getSessionTestRecord(@RequestParam("id") Long id) {
        SessionTestRecordDO sessionTestRecord = sessionTestRecordService.getSessionTestRecord(id);
        return success(BeanUtils.toBean(sessionTestRecord, SessionTestRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测试记录分页")
    @PreAuthorize("@ss.hasPermission('learning:session-test-record:query')")
    public CommonResult<PageResult<SessionTestRecordRespVO>> getSessionTestRecordPage(@Valid SessionTestRecordPageReqVO pageReqVO) {
        PageResult<SessionTestRecordDTO> pageResult = sessionTestRecordService.getSessionTestRecordPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, SessionTestRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出测试记录 Excel")
    @PreAuthorize("@ss.hasPermission('learning:session-test-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSessionTestRecordExcel(@Valid SessionTestRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SessionTestRecordDTO> list = sessionTestRecordService.getSessionTestRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "测试记录.xls", "数据", SessionTestRecordRespVO.class,
                        BeanUtils.toBean(list, SessionTestRecordRespVO.class));
    }

}