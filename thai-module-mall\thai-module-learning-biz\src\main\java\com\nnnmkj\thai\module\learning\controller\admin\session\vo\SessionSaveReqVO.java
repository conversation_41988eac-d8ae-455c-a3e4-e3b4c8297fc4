package com.nnnmkj.thai.module.learning.controller.admin.session.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 学习会话新增/修改 Request VO")
@Data
public class SessionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30957")
    private Long id;

    @Schema(description = "学习集编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "10450")
    @NotNull(message = "学习集编号不能为空")
    private Long setId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19826")
    private Long userId;

    @Schema(description = "学习模式")
    private Integer mode;

    @Schema(description = "会话状态", example = "1")
    private Integer status;

}