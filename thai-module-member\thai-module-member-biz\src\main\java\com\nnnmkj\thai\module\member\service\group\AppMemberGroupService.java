package com.nnnmkj.thai.module.member.service.group;

import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupUsersRespVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupCreateReqVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupUpdateReqVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupUserRespVO;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 用户分组 Service 接口
 *
 * <AUTHOR>
 */
public interface AppMemberGroupService {

    /**
     * 创建班级
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGroup(@Valid AppMemberGroupCreateReqVO createReqVO);

    /**
     * 更新班级
     *
     * @param updateReqVO 更新信息
     */
    void updateGroup(@Valid AppMemberGroupUpdateReqVO updateReqVO);

    /**
     * 删除用户分组
     *
     * @param id 编号
     */
    void deleteGroup(Long id);

    /**
     * 获得用户分组
     *
     * @param id 编号
     * @return 用户分组
     */
    MemberGroupDO getGroup(Long id);

    /**
     * 【会员】加入班级
     *
     * @param groupId 班级编号
     * @return 是否成功
     */
    boolean joinGroup(Long groupId);

    /**
     * 退出班级
     *
     * @param groupId 班级编号
     * @return 是否成功
     */
    boolean quitGroup(Long groupId);

    /**
     * 踢出班级
     *
     * @param userId  被踢用户编号
     * @param groupId 班级编号
     * @return 是否成功
     */
    boolean kickFromGroup(Long userId, Long groupId);

    /**
     * 根据分组编号获得会员用户列表
     *
     * @param groupId 分组编号
     * @param userId  用户编号
     * @return 会员用户列表
     */
    List<AppMemberGroupUserRespVO> getUserListByGroupId(Long groupId, Long userId);

    /**
     * 根据分组编号集合获得会员用户列表
     *
     * @param groupIds 分组编号集合
     * @param userId  用户编号
     * @return 会员用户列表
     */
    List<AppMemberGroupUserRespVO> getUserListByGroupIds(Collection<Long> groupIds, Long userId);

    /**
     * 根据用户编号获取用户分组列表
     *
     * @param userId 用户编号
     * @return 用户分组列表
     */
    List<MemberGroupDO> getGroupListByUserId(Long userId);

    /**
     * 获得用户分组列表
     *
     * @param ids 编号
     * @return 用户分组列表
     */
    List<MemberGroupDO> getGroupList(Collection<Long> ids);

    /**
     * 获取当前登录用户的分组列表
     * @return 分组列表
     */
    List<MemberGroupDO> getGroupsByLoginUser();

    /**
     * 获取当前登录用户的分组编号列表
     * @return 分组编号列表
     */
    List<Long> getGroupIdsByLoginUser();

    /**
     * 校验用户是否有管理权限（必须是创建者）
     * @param groupId 分组编号
     */
    void checkAdminPermission(Long groupId);

    /**
     * 获取指定用户的分组及成员列表
     *
     * @param userId 用户编号
     * @return 用户分组及成员列表
     */
    List<MemberGroupUsersRespVO> getGroupUsersList(Long userId);

}
