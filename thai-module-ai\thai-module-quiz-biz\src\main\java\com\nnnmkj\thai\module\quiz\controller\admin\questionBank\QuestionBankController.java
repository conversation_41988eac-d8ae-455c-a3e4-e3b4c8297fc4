package com.nnnmkj.thai.module.quiz.controller.admin.questionBank;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo.*;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankKnowledgePoint.QuestionBankKnowledgePointDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankOption.QuestionBankOptionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankTag.QuestionBankTagDO;
import com.nnnmkj.thai.module.quiz.service.questionBank.QuestionBankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.apache.tika.sax.BodyContentHandler;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.error;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.module.quiz.enums.ErrorCodeConstants.*;

@Tag(name = "管理后台 - 题目")
@RestController
@RequestMapping("/quiz/question-bank")
@Validated
public class QuestionBankController {

    @Resource
    private QuestionBankService questionBankService;

    @Resource
    private Tika tika;

    @PostMapping("/upload")
    @Operation(summary = "文件上传")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:upload')")
    public CommonResult<List<String>> parseUpload(@RequestParam("file") MultipartFile file) {
        InputStream inputStream = null;
        try {
            String name = file.getName();
            System.out.println("name = " + name);
            String originalFilename = file.getOriginalFilename();
            System.out.println("originalFilename = " + originalFilename);
            inputStream = file.getInputStream();
            Parser parser = tika.getParser();
            System.out.println("parser = " + parser);
            BodyContentHandler handler = new BodyContentHandler(-1);
            parser.parse(inputStream, handler, new Metadata(), new ParseContext());
            String content = handler.toString();
            // 去除空白字符（不包括回车）。多个连续空白字符，只保留一个空白字符。
            String regex = "(?i).*[/\\\\](ppt|docProps|_rels)[/\\\\].*|\\.(xml|rels|mp3|png|jpeg)$";
            content = content.replaceAll("[\\s&&[^\\n]]+", " ")
                    .replaceAll(regex, "");
            if (StringUtils.isNotBlank(content)) {
                // 根据\n拆分为数组
                String[] lines = content.split("\n");
                // 转换为列表
                List<String> linesList = CollUtil.newArrayList(lines);
                // 清空列表中的空白字符
                linesList.removeIf(StringUtils::isBlank);
                return success(linesList);
            }
            return success(new ArrayList<>());
        } catch (Exception e) {
            return error(DOCUMENT_PARSING_FAILED);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @PostMapping("/create")
    @Operation(summary = "创建题目")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:create')")
    public CommonResult<Long> createQuestionBank(@Valid @RequestBody QuestionBankSaveReqVO createReqVO) {
        return success(questionBankService.createQuestionBank(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新题目")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:update')")
    public CommonResult<Boolean> updateQuestionBank(@Valid @RequestBody QuestionBankSaveReqVO updateReqVO) {
        questionBankService.updateQuestionBank(updateReqVO);
        return success(true);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核题目")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:update')")
    public CommonResult<Boolean> auditQuestionBank(@Valid @RequestBody QuestionBankAuditReqVO updateReqVO) {
        questionBankService.auditQuestionBank(updateReqVO);
        return success(true);
    }

    @PutMapping("/audit/batch")
    @Operation(summary = "批量审核题目")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:update')")
    public CommonResult<Boolean> batchAuditQuestionBank(@Valid @RequestBody QuestionBankBatchAuditReqVO reqVO) {
        List<Long> ids = reqVO.getIds();
        if (CollUtil.isEmpty(ids)) {
            throw exception(AUDIT_QUESTION_NOT_SELECTED);
        }
        questionBankService.batchAuditQuestionBank(reqVO);
        return success(true);
    }

    @PutMapping("/batchAddTag")
    @Operation(summary = "批量审核题目")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:update')")
    public CommonResult<Boolean> batchAddTag(@Valid @RequestBody QuestionBankBatchAddTagReqVO reqVO) {
        List<Long> ids = reqVO.getIds();
        if (CollUtil.isEmpty(ids)) {
            throw exception(ADD_TAG_QUESTION_NOT_SELECTED);
        }
        questionBankService.batchAddTag(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除题目")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:delete')")
    public CommonResult<Boolean> deleteQuestionBank(@RequestParam("id") Long id) {
        questionBankService.deleteQuestionBank(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得题目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:query')")
    public CommonResult<QuestionBankRespVO> getQuestionBank(@RequestParam("id") Long id) {
        QuestionBankDO questionBank = questionBankService.getQuestionBank(id);
        return success(BeanUtils.toBean(questionBank, QuestionBankRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得题目分页")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:query')")
    public CommonResult<PageResult<QuestionBankRespVO>> getQuestionBankPage(@Valid QuestionBankPageReqVO pageReqVO) {
        pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        PageResult<QuestionBankDTO> pageResult = questionBankService.getQuestionBankPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        PageResult<QuestionBankRespVO> result = BeanUtils.toBean(pageResult, QuestionBankRespVO.class);

        List<Long> ids = pageResult.getList().stream().map(QuestionBankDO::getId).toList();

        List<QuestionBankKnowledgePointDO> KnowledgePoints = questionBankService.getQuestionBankKnowledgePointListByQuestionIds(ids);
        if (CollUtil.isNotEmpty(KnowledgePoints)) {
            result.getList().forEach(o -> o.setQuestionBankKnowledgePoints(KnowledgePoints.stream().filter(k -> k.getQuestionId().equals(o.getId())).toList()));
        }
        List<QuestionBankOptionDO> questionBankOptions = questionBankService.getQuestionBankOptionListByQuestionIds(ids);
        if (CollUtil.isNotEmpty(questionBankOptions)) {
            result.getList().forEach(o -> o.setQuestionBankOptions(questionBankOptions.stream().filter(k -> k.getQuestionId().equals(o.getId())).toList()));
        }

        List<QuestionBankTagDO> questionBankTags = questionBankService.getQuestionBankTagListByQuestionIds(ids);
        if (CollUtil.isNotEmpty(questionBankTags)) {
            result.getList().forEach(o -> o.setQuestionBankTags(questionBankTags.stream().filter(k -> k.getQuestionId().equals(o.getId())).toList()));
        }
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出题目 Excel")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportQuestionBankExcel(@Valid QuestionBankPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionBankDTO> list = questionBankService.getQuestionBankPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "题目.xls", "数据", QuestionBankRespVO.class,
                BeanUtils.toBean(list, QuestionBankRespVO.class));
    }

    // ==================== 子表（题目知识点） ====================

    @GetMapping("/question-bank-knowledge-point/list-by-question-id")
    @Operation(summary = "获得题目知识点列表")
    @Parameter(name = "questionId", description = "题目ID")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:query')")
    public CommonResult<List<QuestionBankKnowledgePointDO>> getQuestionBankKnowledgePointListByQuestionId(@RequestParam("questionId") Long questionId) {
        return success(questionBankService.getQuestionBankKnowledgePointListByQuestionId(questionId));
    }

    // ==================== 子表（题目选项） ====================

    @GetMapping("/question-bank-option/list-by-question-id")
    @Operation(summary = "获得题目选项列表")
    @Parameter(name = "questionId", description = "题目ID")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:query')")
    public CommonResult<List<QuestionBankOptionDO>> getQuestionBankOptionListByQuestionId(@RequestParam("questionId") Long questionId) {
        return success(questionBankService.getQuestionBankOptionListByQuestionId(questionId));
    }

    // ==================== 子表（题目标签） ====================

    @GetMapping("/question-bank-tag/list-by-question-id")
    @Operation(summary = "获得题目标签列表")
    @Parameter(name = "questionId", description = "题目ID")
    @PreAuthorize("@ss.hasPermission('quiz:question-bank:query')")
    public CommonResult<List<QuestionBankTagDO>> getQuestionBankTagListByQuestionId(@RequestParam("questionId") Long questionId) {
        return success(questionBankService.getQuestionBankTagListByQuestionId(questionId));
    }

}