package com.nnnmkj.thai.module.learning.service.sessionresult;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.sessionresult.SessionResultMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.sessionresult.SessionResultMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.SESSION_RESULT_NOT_EXISTS;

/**
 * 会话成绩 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SessionResultServiceImpl implements SessionResultService {

    @Resource
    private SessionResultMapper sessionResultMapper;

    @Resource
    private SessionResultMapperX sessionResultMapperX;

    @Override
    public Long createSessionResult(SessionResultSaveReqVO createReqVO) {
        // 插入
        SessionResultDO sessionResult = BeanUtils.toBean(createReqVO, SessionResultDO.class);
        sessionResultMapper.insert(sessionResult);
        // 返回
        return sessionResult.getId();
    }

    @Override
    public void updateSessionResult(SessionResultSaveReqVO updateReqVO) {
        // 校验存在
        validateSessionResultExists(updateReqVO.getId());
        // 更新
        SessionResultDO updateObj = BeanUtils.toBean(updateReqVO, SessionResultDO.class);
        sessionResultMapper.updateById(updateObj);
    }

    @Override
    public void deleteSessionResult(Long id) {
        // 校验存在
        validateSessionResultExists(id);
        // 删除
        sessionResultMapper.deleteById(id);
    }

    private void validateSessionResultExists(Long id) {
        if (sessionResultMapper.selectById(id) == null) {
            throw exception(SESSION_RESULT_NOT_EXISTS);
        }
    }

    @Override
    public SessionResultDO getSessionResult(Long id) {
        return sessionResultMapper.selectById(id);
    }

    @Override
    public PageResult<SessionResultDTO> getSessionResultPage(SessionResultPageReqVO pageReqVO) {
        return sessionResultMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<SessionResultDO> getSessionResultList(Long sessionId) {
        return sessionResultMapper.selectList(SessionResultDO::getSessionId, sessionId);
    }

}