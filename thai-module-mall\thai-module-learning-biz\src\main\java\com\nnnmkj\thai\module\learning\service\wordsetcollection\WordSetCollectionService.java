package com.nnnmkj.thai.module.learning.service.wordsetcollection;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDTO;
import jakarta.validation.Valid;

/**
 * 学习集收藏 Service 接口
 *
 * <AUTHOR>
 */
public interface WordSetCollectionService {

    /**
     * 创建学习集收藏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWordSetCollection(@Valid WordSetCollectionSaveReqVO createReqVO);

    /**
     * 更新学习集收藏
     *
     * @param updateReqVO 更新信息
     */
    void updateWordSetCollection(@Valid WordSetCollectionSaveReqVO updateReqVO);

    /**
     * 删除学习集收藏
     *
     * @param id 编号
     */
    void deleteWordSetCollection(Long id);

    /**
     * 获得学习集收藏
     *
     * @param id 编号
     * @return 学习集收藏
     */
    WordSetCollectionDO getWordSetCollection(Long id);

    /**
     * 获得学习集收藏分页
     *
     * @param pageReqVO 分页查询
     * @return 学习集收藏分页
     */
    PageResult<WordSetCollectionDTO> getWordSetCollectionPage(WordSetCollectionPageReqVO pageReqVO);

}