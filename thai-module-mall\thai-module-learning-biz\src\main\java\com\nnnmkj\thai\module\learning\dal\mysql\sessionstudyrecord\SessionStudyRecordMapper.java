package com.nnnmkj.thai.module.learning.dal.mysql.sessionstudyrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionStudyRecordMapper extends BaseMapperX<SessionStudyRecordDO> {

    default PageResult<SessionStudyRecordDO> selectPage(SessionStudyRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<SessionStudyRecordDO>()
                .eqIfPresent(SessionStudyRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionStudyRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionStudyRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionStudyRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(SessionStudyRecordDO::getUserAnswer, reqVO.getUserAnswer())
                .eqIfPresent(SessionStudyRecordDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(SessionStudyRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionStudyRecordDO::getId));
    }

}