package com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.nnnmkj.thai.framework.excel.core.annotations.DictFormat;
import com.nnnmkj.thai.framework.excel.core.convert.DictConvert;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankKnowledgePoint.QuestionBankKnowledgePointDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankOption.QuestionBankOptionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankTag.QuestionBankTagDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 题目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionBankRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "9762")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "AI出题ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5445")
    @ExcelProperty("AI出题ID")
    private Long aiQuestionId;

    @Schema(description = "题目类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "题目类型", converter = DictConvert.class)
    @DictFormat("assignment_question_type")
    private Integer questionType;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6522")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "创建方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "创建方式", converter = DictConvert.class)
    @DictFormat("create_method")
    private Integer createMethod;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("用户昵称")
    private String nickname;

    @Schema(description = "题干", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("题干")
    private String questionStem;

    @Schema(description = "参考答案", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("参考答案")
    private String answer;

    @Schema(description = "题目出处")
    @ExcelProperty("题目出处")
    private String source;

    @Schema(description = "试题解析")
    @ExcelProperty("试题解析")
    private String analysis;

    @Schema(description = "难易度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "难易度", converter = DictConvert.class)
    @DictFormat("assignment_question_difficulty")
    private Integer difficulty;

    @Schema(description = "是否公开", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否公开")
    private Boolean publicStatus;

    @Schema(description = "未通过原因", example = "不好")
    @ExcelProperty("未通过原因")
    private String rejectReason;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("audit_status")
    private Integer auditStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "题目知识点列表")
    private List<QuestionBankKnowledgePointDO> questionBankKnowledgePoints;

    @Schema(description = "题目选项列表")
    private List<QuestionBankOptionDO> questionBankOptions;

    @Schema(description = "题目标签列表")
    private List<QuestionBankTagDO> questionBankTags;

}