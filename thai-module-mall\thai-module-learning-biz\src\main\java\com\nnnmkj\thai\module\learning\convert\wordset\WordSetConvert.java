package com.nnnmkj.thai.module.learning.convert.wordset;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetRespVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetRespVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDTO;
import com.nnnmkj.thai.module.learning.enums.IsStoreStatusEnum;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.nnnmkj.thai.framework.common.util.collection.CollectionUtils.convertMap;

@Mapper
public interface WordSetConvert {

    WordSetConvert INSTANCE = Mappers.getMapper(WordSetConvert.class);

    PageResult<WordSetRespVO> convertPage(PageResult<WordSetDO> page);

    default PageResult<WordSetRespVO> convertPage(PageResult<WordSetDO> pageResult,
                                                  List<MemberUserRespDTO> userList) {
        PageResult<WordSetRespVO> result = convertPage(pageResult);
        // 处理关联数据
        Map<Long, String> userMap = convertMap(userList, MemberUserRespDTO::getId, MemberUserRespDTO::getNickname);
        // 填充关联数据
        result.getList().forEach(p -> p.setNickname(userMap.get(p.getUserId())));
        return result;
    }

    PageResult<AppWordSetRespVO> convertAppPage(PageResult<WordSetDTO> page);

    default PageResult<AppWordSetRespVO> convertAppPage(PageResult<WordSetDTO> pageResult,
                                                        Set<Long> storeSetIds) {
        PageResult<AppWordSetRespVO> result = convertAppPage(pageResult);
        // 填充关联数据
        result.getList().forEach(p -> {
            p.setIsStore(CollUtil.isNotEmpty(storeSetIds) && storeSetIds.contains(p.getId())
                    ? IsStoreStatusEnum.YES.getType()
                    : IsStoreStatusEnum.NO.getType());
            p.setViewCount(p.getViewCount() == null ? 0 : p.getViewCount());
            p.setStudyCount(p.getStudyCount() == null ? 0 : p.getStudyCount());
            p.setCollectionCount(p.getCollectionCount() == null ? 0 : p.getCollectionCount());
        });
        return result;
    }

}
