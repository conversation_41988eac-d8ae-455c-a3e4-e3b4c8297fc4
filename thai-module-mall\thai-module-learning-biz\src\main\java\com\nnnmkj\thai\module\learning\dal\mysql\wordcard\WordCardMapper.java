package com.nnnmkj.thai.module.learning.dal.mysql.wordcard;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.wordcard.vo.WordCardPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 单词卡 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordCardMapper extends BaseMapperX<WordCardDO> {

    default PageResult<WordCardDO> selectPage(WordCardPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<WordCardDO>()
                .eqIfPresent(WordCardDO::getWordSetId, reqVO.getWordSetId())
                .eqIfPresent(WordCardDO::getWord, reqVO.getWord())
                .likeIfPresent(WordCardDO::getCreator, creatorQuery)
                .eqIfPresent(WordCardDO::getPhoneticSymbol, reqVO.getPhoneticSymbol())
                .betweenIfPresent(WordCardDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordCardDO::getId));
    }

    default List<WordCardDO> selectListByWordSetId(Long wordSetId) {
        return selectList(WordCardDO::getWordSetId, wordSetId);
    }

    default void deleteByWordSetId(Long wordSetId) {
        delete(WordCardDO::getWordSetId, wordSetId);
    }

}