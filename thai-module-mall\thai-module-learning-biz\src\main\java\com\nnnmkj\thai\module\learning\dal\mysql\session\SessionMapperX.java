package com.nnnmkj.thai.module.learning.dal.mysql.session;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习会话 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionMapperX extends BaseMapperX<SessionDTO> {

    default PageResult<SessionDTO> selectPage(SessionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<SessionDTO> wrapper = new MPJLambdaWrapperX<SessionDTO>()
                .selectAll(SessionDO.class)
                .selectAs("t1.nickname", SessionDTO::getNickname)
                .selectAs(WordSetDO::getTitle, SessionDTO::getSetTitle)
                .eqIfPresent(SessionDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionDO::getCreator, creatorQuery)
                .eqIfPresent(SessionDO::getMode, reqVO.getMode())
                .eqIfPresent(SessionDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SessionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, SessionDO::getSetId);
        return selectPage(reqVO, wrapper);
    }

}