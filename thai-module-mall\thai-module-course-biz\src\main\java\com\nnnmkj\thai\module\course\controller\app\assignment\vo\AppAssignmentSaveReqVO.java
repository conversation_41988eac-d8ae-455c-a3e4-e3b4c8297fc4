package com.nnnmkj.thai.module.course.controller.app.assignment.vo;

import com.nnnmkj.thai.framework.common.validation.InEnum;
import com.nnnmkj.thai.module.course.enums.AssignmentScoringMechanismEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "用户 App - 课程作业新增 Request VO")
@Data
public class AppAssignmentSaveReqVO {

    @Schema(description = "编号", example = "21531")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21531")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "作业名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotBlank(message = "作业名称不能为空")
    private String name;

    @Schema(description = "评分机制", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评分机制不能为空")
    @InEnum(value = AssignmentScoringMechanismEnum.class)
    private Integer scoringMechanism;

}