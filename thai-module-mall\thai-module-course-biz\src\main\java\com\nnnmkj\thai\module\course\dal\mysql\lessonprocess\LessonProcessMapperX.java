package com.nnnmkj.thai.module.course.dal.mysql.lessonprocess;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.admin.lessonprocess.vo.LessonProcessPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess.LessonProcessDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessonprocess.LessonProcessDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程进度 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonProcessMapperX extends BaseMapperX<LessonProcessDTO> {

    default PageResult<LessonProcessDTO> selectPage(LessonProcessPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), WebFrameworkUtils.getLoginUserType());
        MPJLambdaWrapper<LessonProcessDTO> wrapper = new MPJLambdaWrapperX<LessonProcessDTO>()
                .selectAll(LessonProcessDO.class)
                .selectAs("t1.nickname", LessonProcessDTO::getNickname)
                .selectAs(LessonDO::getTitle, LessonProcessDTO::getCourseTitle)
                .eqIfPresent(LessonProcessDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(LessonProcessDO::getUserId, reqVO.getUserId())
                .eqIfPresent(LessonProcessDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(LessonProcessDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(LessonProcessDO::getCreator, creatorQuery)
                .orderByDesc(LessonProcessDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(LessonDO.class, "t2", LessonDO::getId, LessonProcessDO::getCourseId);
        return selectPage(reqVO, wrapper);
    }

}