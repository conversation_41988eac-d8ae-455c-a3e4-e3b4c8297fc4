package com.nnnmkj.thai.module.course.controller.app.assignmentrelease.vo;

import com.nnnmkj.thai.framework.common.validation.InEnum;
import com.nnnmkj.thai.module.course.enums.AssignmentDistributionTargetEnum;
import com.nnnmkj.thai.module.member.api.group.dto.MemberGroupRespDTO;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 App - 课程作业发布 Response VO")
@Data
public class AppAssignmentReleaseDetailRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标题不能为空")
    private String title;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime endTime;

    @Schema(description = "发放对象", requiredMode = Schema.RequiredMode.REQUIRED)
    @InEnum(AssignmentDistributionTargetEnum.class)
    private Integer distributionTarget;

    @Schema(description = "发放对象-分组列表", example = "[1,2,3]")
    private List<MemberGroupRespDTO> groups;

    @Schema(description = "发放对象-用户列表", example = "[1,2,3]")
    private List<MemberUserRespDTO> users;

    @Schema(description = "及格分数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer passMark;

    @Schema(description = "是否督促", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isUrge;

    @Schema(description = "督促截止时间（H）")
    private Integer urgeDeadline;

    @Schema(description = "是否随机抽题", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isRandom;

    @Schema(description = "随机抽题数量")
    private Integer randomCount;

    @Schema(description = "题目数量")
    private Integer questionCount;

}