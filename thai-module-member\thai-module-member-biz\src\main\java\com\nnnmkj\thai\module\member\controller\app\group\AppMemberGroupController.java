package com.nnnmkj.thai.module.member.controller.app.group;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.module.course.api.assignment.AssignmentApi;
import com.nnnmkj.thai.module.learning.api.lesson.LessonApi;
import com.nnnmkj.thai.module.learning.api.membergroupwordset.MemberGroupWordSetApi;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupRespVO;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupUsersRespVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupCreateReqVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupRespVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupUpdateReqVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupUserRespVO;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import com.nnnmkj.thai.module.member.service.group.AppMemberGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.ListUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;


@Tag(name = "用户 APP - 用户分组（班级）")
@RestController
@RequestMapping("/member/group")
@Validated
public class AppMemberGroupController {

    @Resource
    private AppMemberGroupService groupService;

    @Resource
    private MemberGroupWordSetApi memberGroupWordSetApi;

    @Resource
    private LessonApi lessonApi;
    
    @Resource
    private AssignmentApi assignmentApi;

    @PostMapping("/my/joined")
    @Operation(summary = "获取当前用户加入的分组（非自己创建的分组）")
    public CommonResult<List<AppMemberGroupRespVO>> getMyJoinedGroups() {
        // 获取当前用户加入的所有分组ID
        List<MemberGroupDO> allJoinedGroups = groupService.getGroupsByLoginUser();

        // 查询当前用户创建的所有分组
        Long currentUserId = getLoginUserId();
        List<MemberGroupDO> userCreatedGroups = groupService.getGroupListByUserId(currentUserId);

        // 从加入的分组中移除自己创建的分组，得到真正加入的其他人的分组
        List<MemberGroupDO> pureJoinedGroups = ListUtils.removeAll(allJoinedGroups, userCreatedGroups);
        if (CollUtil.isEmpty(pureJoinedGroups)) {
            return success(Collections.emptyList());
        }

        List<AppMemberGroupRespVO> result = BeanUtil.copyToList(pureJoinedGroups, AppMemberGroupRespVO.class);
        
        // 批量获取分组的学习集数量（仅包含可见范围为所有人或组内可见的学习集）
        List<Long> groupIds = pureJoinedGroups.stream().map(MemberGroupDO::getId).collect(Collectors.toList());
        Map<Long, Integer> studySetCountMap = memberGroupWordSetApi.getVisibleStudySetCountMap(groupIds);
        
        // 批量获取分组的课程数量
        Map<Long, Integer> lessonCountMap = lessonApi.getLessonCountMap(groupIds);
        
        // 批量获取分组的作业数量
        Map<Long, Integer> assignmentCountMap = assignmentApi.getAssignmentCountMap(groupIds);
        
        // 设置每个分组的学习集数量和课程数量
        for (AppMemberGroupRespVO vo : result) {
            vo.setStudySetCount(studySetCountMap.getOrDefault(vo.getId(), 0));
            vo.setLessonCount(lessonCountMap.getOrDefault(vo.getId(), 0));
            vo.setAssignmentCount(assignmentCountMap.getOrDefault(vo.getId(), 0));
        }
        
        return success(result);
    }

    @PostMapping("/my/created")
    @Operation(summary = "获取当前用户创建的分组")
    public CommonResult<List<AppMemberGroupRespVO>> getMyCreated() {
        Long loginUserId = getLoginUserId();
        List<MemberGroupDO> groupList = groupService.getGroupListByUserId(loginUserId);
        List<AppMemberGroupRespVO> result = BeanUtil.copyToList(groupList, AppMemberGroupRespVO.class);
        
        // 批量获取分组的学习集数量
        if (CollUtil.isNotEmpty(groupList)) {
            List<Long> groupIds = groupList.stream().map(MemberGroupDO::getId).collect(Collectors.toList());
            Map<Long, Integer> studySetCountMap = memberGroupWordSetApi.getVisibleStudySetCountMap(groupIds);
            Map<Long, Integer> lessonCountMap = lessonApi.getLessonCountMap(groupIds);
            Map<Long, Integer> assignmentCountMap = assignmentApi.getAssignmentCountMap(groupIds);
            
            // 设置每个分组的学习集数量和课程数量
            for (AppMemberGroupRespVO vo : result) {
                vo.setStudySetCount(studySetCountMap.getOrDefault(vo.getId(), 0));
                vo.setLessonCount(lessonCountMap.getOrDefault(vo.getId(), 0));
                vo.setAssignmentCount(assignmentCountMap.getOrDefault(vo.getId(), 0));
            }
        }
        
        return success(result);
    }

    @PostMapping("/create")
    @Operation(summary = "创建用户分组")
    public CommonResult<Long> createGroup(@Valid @RequestBody AppMemberGroupCreateReqVO createReqVO) {
        return success(groupService.createGroup(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户分组")
    public CommonResult<Boolean> updateGroup(@Valid @RequestBody AppMemberGroupUpdateReqVO updateReqVO) {
        // 判断是否有权限更新，只有创建者才能更新
        groupService.checkAdminPermission(updateReqVO.getId());
        groupService.updateGroup(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户分组")
    public CommonResult<Boolean> deleteGroup(@RequestParam("id") @NotNull(message = "编号不能为空") Long id) {
        // 判断是否有权限删除，只有创建者才能删除
        groupService.checkAdminPermission(id);
        groupService.deleteGroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户分组")
    public CommonResult<MemberGroupRespVO> getGroup(@RequestParam("id") @NotNull(message = "编号不能为空") Long id) {
        MemberGroupDO group = groupService.getGroup(id);
        MemberGroupRespVO vo = BeanUtil.toBean(group, MemberGroupRespVO.class);
        // 获取分组的学习集数量（仅包含可见范围为所有人或组内可见的学习集）
        int studySetCount = memberGroupWordSetApi.getVisibleStudySetCount(id);
        vo.setStudySetCount(studySetCount);
        // 获取分组的课程数量
        int lessonCount = lessonApi.getLessonCount(id);
        vo.setLessonCount(lessonCount);
        // 获取分组的作业数量
        int assignmentCount = assignmentApi.getAssignmentCount(id);
        vo.setAssignmentCount(assignmentCount);
        return success(vo);
    }

    @GetMapping("/list/user")
    @Operation(summary = "查询当前用户创建的分组及其成员列表")
    public CommonResult<List<MemberGroupUsersRespVO>> getGroupsUserList() {
        return success(groupService.getGroupUsersList(getLoginUserId()));
    }

    @GetMapping("/list/{id}/user")
    @Operation(summary = "获取指定分组下的用户列表")
    public CommonResult<List<AppMemberGroupUserRespVO>> getGroupUserListById(@PathVariable @NotNull(message = "编号不能为空") Long id) {
        return success(groupService.getUserListByGroupId(getLoginUserId(), id));
    }

    @PostMapping("/join")
    @Operation(summary = "加入分组")
    public CommonResult<Boolean> joinGroup(@RequestParam @NotNull(message = "编号不能为空") Long groupId) {
        return success(groupService.joinGroup(groupId));
    }
    
    @PostMapping("/quit")
    @Operation(summary = "退出分组")
    public CommonResult<Boolean> quitGroup(@RequestParam @NotNull(message = "编号不能为空") Long groupId) {
        return success(groupService.quitGroup(groupId));
    }
    
    @PostMapping("/kick")
    @Operation(summary = "踢出分组")
    public CommonResult<Boolean> kickFromGroup(@RequestParam @NotNull(message = "班级编号不能为空") Long groupId,
                                               @RequestParam @NotNull(message = "用户编号不能为空") Long userId) {
        return success(groupService.kickFromGroup(userId, groupId));
    }

}
