package com.nnnmkj.thai.module.quiz.dal.mysql.aiquestion;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionPageReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * AI出题 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiQuestionMapper extends BaseMapperX<AiQuestionDO> {

    default LambdaQueryWrapperX<AiQuestionDO> buildCommonCondition(AiQuestionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return new LambdaQueryWrapperX<AiQuestionDO>()
                .likeIfPresent(AiQuestionDO::getCreator, creatorQuery)
                .eqIfPresent(AiQuestionDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AiQuestionDO::getCurrentNode, reqVO.getCurrentNode())
                .eqIfPresent(AiQuestionDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AiQuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AiQuestionDO::getId);
    }

    default PageResult<AiQuestionDO> selectPage(AiQuestionPageReqVO reqVO) {
        return selectPage(reqVO, buildCommonCondition(reqVO));
    }

    default List<AiQuestionDO> selectList(AiQuestionPageReqVO reqVO) {
        return selectList(buildCommonCondition(reqVO));
    }

}