package com.nnnmkj.thai.module.learning.dal.mysql.sessionresult;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 会话成绩 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionResultMapper extends BaseMapperX<SessionResultDO> {

    default PageResult<SessionResultDO> selectPage(SessionResultPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<SessionResultDO>()
                .eqIfPresent(SessionResultDO::getSessionId, reqVO.getSessionId())
                .likeIfPresent(SessionResultDO::getCreator, creatorQuery)
                .eqIfPresent(SessionResultDO::getSetId, reqVO.getSetId())
                .eqIfPresent(SessionResultDO::getTimeTaken, reqVO.getTimeTaken())
                .eqIfPresent(SessionResultDO::getAllCount, reqVO.getAllCount())
                .eqIfPresent(SessionResultDO::getCorrectCount, reqVO.getCorrectCount())
                .eqIfPresent(SessionResultDO::getErrorCount, reqVO.getErrorCount())
                .betweenIfPresent(SessionResultDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionResultDO::getId));
    }

}