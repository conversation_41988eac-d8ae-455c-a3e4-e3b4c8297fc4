package com.nnnmkj.thai.module.course.dal.mysql.membergroupassignmentrelease;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.membergroupassignmentrelease.vo.MemberGroupAssignmentReleasePageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergroupassignmentrelease.MemberGroupAssignmentReleaseDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.stream.Collectors;

/**
 * 课程作业发布班级关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberGroupAssignmentReleaseMapper extends BaseMapperX<MemberGroupAssignmentReleaseDO> {

    default int selectCountByGroupId(Long groupId) {
        return Math.toIntExact(selectCount(new LambdaQueryWrapperX<MemberGroupAssignmentReleaseDO>()
                .apply("FIND_IN_SET({0}, group_ids)", groupId)));
    }

    default PageResult<MemberGroupAssignmentReleaseDO> selectPage(MemberGroupAssignmentReleasePageReqVO reqVO) {
        LambdaQueryWrapperX<MemberGroupAssignmentReleaseDO> wrapper = new LambdaQueryWrapperX<MemberGroupAssignmentReleaseDO>()
                .eqIfPresent(MemberGroupAssignmentReleaseDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(MemberGroupAssignmentReleaseDO::getAssignmentReleaseId, reqVO.getAssignmentReleaseId())
                .betweenIfPresent(MemberGroupAssignmentReleaseDO::getCreateTime, reqVO.getCreateTime());


        // 用户 ID 多选（FIND_IN_SET）
        if (CollUtil.isNotEmpty(reqVO.getUserIds())) {
            String userSql = reqVO.getUserIds().stream()
                    .map(userId -> "FIND_IN_SET(" + userId + ", user_ids)")
                    .collect(Collectors.joining(" OR "));
            wrapper.apply(StrUtil.isNotEmpty(userSql), userSql);
        }

        // 分组 ID 多选（FIND_IN_SET）
        if (CollUtil.isNotEmpty(reqVO.getGroupIds())) {
            String groupSql = reqVO.getGroupIds().stream()
                    .map(groupId -> "FIND_IN_SET(" + groupId + ", group_ids)")
                    .collect(Collectors.joining(" OR "));
            wrapper.apply(StrUtil.isNotEmpty(groupSql), groupSql);
        }

        wrapper.orderByDesc(MemberGroupAssignmentReleaseDO::getId);
        return selectPage(reqVO, wrapper);
    }

    //根据作业id获取作业班级关联
    default MemberGroupAssignmentReleaseDO selectByAssignmentReleaseId(Long assignmentReleaseId) {
        return selectOne(new LambdaQueryWrapperX<MemberGroupAssignmentReleaseDO>()
                .eq(MemberGroupAssignmentReleaseDO::getAssignmentReleaseId, assignmentReleaseId));
    }
}