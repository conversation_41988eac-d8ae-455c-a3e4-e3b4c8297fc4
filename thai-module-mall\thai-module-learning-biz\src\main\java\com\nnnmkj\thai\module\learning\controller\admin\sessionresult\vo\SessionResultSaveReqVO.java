package com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 会话成绩新增/修改 Request VO")
@Data
public class SessionResultSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13519")
    private Long id;

    @Schema(description = "学习会话ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7617")
    @NotNull(message = "学习会话ID不能为空")
    private Long sessionId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15958")
    private Long userId;

    @Schema(description = "学习集ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12795")
    @NotNull(message = "学习集ID不能为空")
    private Long setId;

    @Schema(description = "完成时间(秒)")
    private Integer timeTaken;

    @Schema(description = "总题目数", example = "11954")
    private Integer allCount;

    @Schema(description = "答对数量", example = "26740")
    private Integer correctCount;

    @Schema(description = "答错数量", example = "21480")
    private Integer errorCount;

}