package com.nnnmkj.thai.module.learning.controller.app.search;

import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.learning.controller.app.search.vo.AppSearchReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetRespVO;
import com.nnnmkj.thai.module.learning.service.search.AppSearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 搜索")
@RestController
@RequestMapping("/learning/search")
@Validated
public class AppSearchController {

    @Resource
    private AppSearchService appSearchService;

    @PostMapping("/learning")
    @Operation(summary = "关键词搜索学习集")
    public CommonResult<PageResult<AppWordSetRespVO>> searchLearning(@Valid @RequestBody AppSearchReqVO reqVO) {
        reqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        reqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        return success(appSearchService.searchLearning(reqVO));
    }

}