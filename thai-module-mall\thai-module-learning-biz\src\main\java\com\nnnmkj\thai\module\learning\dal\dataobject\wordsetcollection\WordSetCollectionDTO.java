package com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection;

import lombok.*;

/**
 * 学习集收藏 DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class WordSetCollectionDTO extends WordSetCollectionDO {

    /**
     * 学习集标题
     */
    private String setTitle;
    /**
     * 用户昵称
     */
    private String nickname;

}