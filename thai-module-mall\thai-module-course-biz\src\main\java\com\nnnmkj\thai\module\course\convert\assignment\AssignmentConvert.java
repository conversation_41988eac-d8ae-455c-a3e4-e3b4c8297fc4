package com.nnnmkj.thai.module.course.convert.assignment;

import com.google.common.collect.Maps;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Mapper
public interface AssignmentConvert {

    AssignmentConvert INSTANCE = Mappers.getMapper(AssignmentConvert.class);

    PageResult<AppAssignmentRespVO> convertPage(PageResult<AssignmentDO> page);

    default PageResult<AppAssignmentRespVO> convertPage(PageResult<AssignmentDO> page, List<AssignmentQuestionDO> questions) {
        PageResult<AppAssignmentRespVO> result = convertPage(page);

        // 计算每个作业的题目总分
        Map<Long, List<Double>> assignmentScoreMap = CollectionUtils.convertMultiMap(
                questions, AssignmentQuestionDO::getAssignmentId, AssignmentQuestionDO::getScore
        );

        Map<Long, Double> assignmentTotalScoreMap = Maps.transformValues(
                assignmentScoreMap, scores -> scores.stream()
                        .filter(Objects::nonNull)
                        .mapToDouble(Double::doubleValue)
                        .sum()
        );

        // 计算每个作业的题目数量
        Map<Long, Long> assignmentQuestionCountMap = questions.stream()
                .collect(Collectors.groupingBy(
                        AssignmentQuestionDO::getAssignmentId,
                        Collectors.counting()
                ));

        // 设置总分到返回结果中
        result.getList().forEach(
                vo -> {
                    vo.setFullMarks(assignmentTotalScoreMap.getOrDefault(vo.getId(), 0.0));
                    vo.setQuestionCount(Math.toIntExact(assignmentQuestionCountMap.getOrDefault(vo.getId(), 0L)));
                }
        );

        return result;
    }

}