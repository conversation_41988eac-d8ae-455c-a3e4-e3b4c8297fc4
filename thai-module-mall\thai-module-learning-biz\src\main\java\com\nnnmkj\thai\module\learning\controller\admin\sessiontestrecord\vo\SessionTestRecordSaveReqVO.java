package com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 测试记录新增/修改 Request VO")
@Data
public class SessionTestRecordSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19002")
    private Long id;

    @Schema(description = "学习集ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16473")
    @NotNull(message = "学习集ID不能为空")
    private Long setId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21799")
    private Long userId;

    @Schema(description = "学习会话ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27526")
    @NotNull(message = "学习会话ID不能为空")
    private Long sessionId;

    @Schema(description = "题目ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29095")
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    @Schema(description = "用户答案",requiredMode = Schema.RequiredMode.REQUIRED, example = "A")
    @NotNull(message = "用户答案不能为空")
    private String userAnswer;

    @Schema(description = "是否正确")
    private Integer isCorrect;

}