package com.nnnmkj.thai.module.learning.controller.admin.session;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.question.QuestionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.question.QuestionDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.questionoption.QuestionOptionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.questionoption.QuestionOptionDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionprogress.SessionProgressDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionquestion.SessionQuestionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcarddefinition.WordCardDefinitionDO;
import com.nnnmkj.thai.module.learning.service.question.QuestionService;
import com.nnnmkj.thai.module.learning.service.questionoption.QuestionOptionService;
import com.nnnmkj.thai.module.learning.service.session.SessionService;
import com.nnnmkj.thai.module.learning.service.sessioncardrecord.SessionCardRecordService;
import com.nnnmkj.thai.module.learning.service.sessionmatchrecord.SessionMatchRecordService;
import com.nnnmkj.thai.module.learning.service.sessionprogress.SessionProgressService;
import com.nnnmkj.thai.module.learning.service.sessionquestion.SessionQuestionService;
import com.nnnmkj.thai.module.learning.service.sessionresult.SessionResultService;
import com.nnnmkj.thai.module.learning.service.sessionstudyrecord.SessionStudyRecordService;
import com.nnnmkj.thai.module.learning.service.sessiontestrecord.SessionTestRecordService;
import com.nnnmkj.thai.module.learning.service.wordcard.WordCardService;
import com.nnnmkj.thai.module.learning.service.wordcarddefinition.WordCardDefinitionService;
import com.nnnmkj.thai.module.learning.service.wordset.WordSetService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 学习会话")
@RestController
@RequestMapping("/learning/session")
@Validated
public class SessionController {

    @Resource
    private SessionService sessionService;

    @Resource
    private SessionResultService sessionResultService;

    @Resource
    private SessionProgressService sessionProgressService;

    @Resource
    private SessionQuestionService sessionQuestionService;

    @Resource
    private SessionCardRecordService sessionCardRecordService;

    @Resource
    private WordCardService wordCardService;

    @Resource
    private WordCardDefinitionService wordCardDefinitionService;

    @Resource
    private SessionMatchRecordService sessionMatchRecordService;

    @Resource
    private SessionStudyRecordService sessionStudyRecordService;

    @Resource
    private SessionTestRecordService sessionTestRecordService;

    @Resource
    private QuestionService questionService;

    @Resource
    private QuestionOptionService questionOptionService;

    @PostMapping("/create")
    @Operation(summary = "创建学习会话")
    @PreAuthorize("@ss.hasPermission('learning:session:create')")
    public CommonResult<Long> createSession(@Valid @RequestBody SessionSaveReqVO createReqVO) {
        return success(sessionService.createSession(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新学习会话")
    @PreAuthorize("@ss.hasPermission('learning:session:update')")
    public CommonResult<Boolean> updateSession(@Valid @RequestBody SessionSaveReqVO updateReqVO) {
        sessionService.updateSession(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除学习会话")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:session:delete')")
    public CommonResult<Boolean> deleteSession(@RequestParam("id") Long id) {
        sessionService.deleteSession(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得学习会话")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:session:query')")
    public CommonResult<SessionRespVO> getSession(@RequestParam("id") Long id) {
        SessionDO session = sessionService.getSession(id);
        SessionRespVO sessionRespVO = BeanUtils.toBean(session, SessionRespVO.class);
        // 获取学习会话题目关联
        List<SessionQuestionDO> sessionQuestionList = sessionQuestionService.getSessionQuestionList(id);
        // 获取关联的题目ID列表
        List<Long> questionIds = sessionQuestionList.stream().map(SessionQuestionDO::getQuestionId).toList();
        if (CollUtil.isNotEmpty(questionIds)) {
            List<QuestionDTO> questions = new ArrayList<>();
            // 获取关联的题目列表
            List<QuestionDO> questionDOS = questionService.getQuestionListByIds(questionIds);
            // 获取关联的题目选项列表
            List<QuestionOptionDO> questionOptionDOS = questionOptionService.getQuestionOptionListByQuestionId(questionIds);
            // 转换为DTO
            for (QuestionDO questionDO : questionDOS) {
                QuestionDTO questionDTO = BeanUtils.toBean(questionDO, QuestionDTO.class);
                List<QuestionOptionDTO> options = questionOptionDOS.stream()
                        .filter(questionOptionDO -> questionOptionDO.getQuestionId().equals(questionDO.getId()))
                        .map(questionOptionDO -> BeanUtils.toBean(questionOptionDO, QuestionOptionDTO.class))
                        .toList();
                questionDTO.setOptions(options);
                questions.add(questionDTO);
            }
            sessionRespVO.setQuestions(questions);
        }
        // 获取关联的成绩列表
        List<SessionResultDO> sessionResultDOS = sessionResultService.getSessionResultList(id);
        sessionRespVO.setResults(sessionResultDOS);
        // 获取关联的进度列表
        List<SessionProgressDO> sessionProgressDOS = sessionProgressService.getSessionProgressList(id);
        sessionRespVO.setProgresses(sessionProgressDOS);
        // 获取关联的单词卡记录列表
        List<SessionCardRecordDO> sessionCardRecordDOS = sessionCardRecordService.getSessionCardRecordList(id);
        List<Long> wordCardIds = sessionCardRecordDOS.stream().map(SessionCardRecordDO::getWordCardId).toList();
        if (CollUtil.isNotEmpty(wordCardIds)) {
            List<SessionCardRecordDTO> cardRecords = new ArrayList<>();
            // 获取关联的单词卡列表
            List<WordCardDO> wordCardList = wordCardService.getWordCardList(wordCardIds);
            List<WordCardDefinitionDO> definitionList = wordCardDefinitionService.getWordCardDefinitionListByWordCardIds(wordCardIds);
            // 转换为DTO
            for (SessionCardRecordDO sessionCardRecordDO : sessionCardRecordDOS) {
                SessionCardRecordDTO sessionCardRecordDTO = BeanUtils.toBean(sessionCardRecordDO, SessionCardRecordDTO.class);
                WordCardDTO wordCardDTO = BeanUtils.toBean(wordCardList.stream()
                        .filter(wordCardDO -> wordCardDO.getId().equals(sessionCardRecordDO.getWordCardId()))
                        .findFirst().orElseThrow(), WordCardDTO.class);
                wordCardDTO.setDefinitions(definitionList.stream()
                        .filter(wordCardDefinitionDO -> wordCardIds.contains(wordCardDefinitionDO.getWordCardId()))
                        .map(wordCardDefinitionDO -> BeanUtils.toBean(wordCardDefinitionDO, WordCardDefinitionDO.class))
                        .toList());
                sessionCardRecordDTO.setWord(wordCardDTO.getWord());
                sessionCardRecordDTO.setDefinitions(wordCardDTO.getDefinitions());
                cardRecords.add(sessionCardRecordDTO);
            }
            sessionRespVO.setCardRecords(cardRecords);
        }
        // 获取关联的学习记录列表
        List<SessionStudyRecordDO> sessionStudyRecordDOS = sessionStudyRecordService.getSessionStudyRecordList(id);
        List<Long> studyQuestionIds = sessionStudyRecordDOS.stream().map(SessionStudyRecordDO::getQuestionId).toList();
        if (CollUtil.isNotEmpty(studyQuestionIds)) {
            List<SessionStudyRecordDTO> studyRecords = new ArrayList<>();
            // 获取关联的题目列表
            List<QuestionDO> questionDOS = questionService.getQuestionListByIds(studyQuestionIds);
            List<QuestionOptionDO> optionDOS = questionOptionService.getQuestionOptionListByQuestionId(studyQuestionIds);
            // 转换为DTO
            for (SessionStudyRecordDO sessionStudyRecordDO : sessionStudyRecordDOS) {
                SessionStudyRecordDTO sessionStudyRecordDTO = BeanUtils.toBean(sessionStudyRecordDO, SessionStudyRecordDTO.class);
                // 获取题目
                Long questionId = sessionStudyRecordDTO.getQuestionId();
                QuestionDTO questionDTO = BeanUtils.toBean(questionDOS.stream()
                        .filter(questionDO -> questionDO.getId().equals(questionId))
                        .findFirst().orElseThrow(), QuestionDTO.class);
                sessionStudyRecordDTO.setContent(questionDTO.getContent());
                sessionStudyRecordDTO.setAnswer(questionDTO.getAnswer());
                // 获取题目选项列表
                List<QuestionOptionDO> options = optionDOS.stream()
                        .filter(questionOptionDO -> questionOptionDO.getQuestionId().equals(questionId))
                        .toList();
                sessionStudyRecordDTO.setOptions(options);
                sessionStudyRecordDTO.setQuestionType(questionDTO.getQuestionType());
                studyRecords.add(sessionStudyRecordDTO);
            }
            sessionRespVO.setStudyRecords(studyRecords);
        }
        // 获取关联的测试记录列表
        List<SessionTestRecordDO> sessionTestRecordDOS = sessionTestRecordService.getSessionTestRecordList(id);
        List<Long> testQuestionIds = sessionTestRecordDOS.stream().map(SessionTestRecordDO::getQuestionId).toList();
        if (CollUtil.isNotEmpty(testQuestionIds)) {
            List<SessionTestRecordDTO> testRecords = new ArrayList<>();
            List<QuestionDO> questionDOS = questionService.getQuestionListByIds(testQuestionIds);
            List<QuestionOptionDO> optionDOS = questionOptionService.getQuestionOptionListByQuestionId(testQuestionIds);
            for (SessionTestRecordDO sessionTestRecordDO : sessionTestRecordDOS) {
                SessionTestRecordDTO sessionTestRecordDTO = BeanUtils.toBean(sessionTestRecordDO, SessionTestRecordDTO.class);
                Long questionId = sessionTestRecordDTO.getQuestionId();
                QuestionDTO questionDTO = BeanUtils.toBean(questionDOS.stream()
                        .filter(questionDO -> questionDO.getId().equals(questionId))
                        .findFirst().orElseThrow(), QuestionDTO.class);
                sessionTestRecordDTO.setContent(questionDTO.getContent());
                sessionTestRecordDTO.setAnswer(questionDTO.getAnswer());
                List<QuestionOptionDO> options = optionDOS.stream()
                        .filter(questionOptionDO -> questionOptionDO.getQuestionId().equals(questionId))
                        .toList();
                sessionTestRecordDTO.setOptions(options);
                sessionTestRecordDTO.setQuestionType(questionDTO.getQuestionType());
                testRecords.add(sessionTestRecordDTO);
            }
            sessionRespVO.setTestRecords(testRecords);
        }

        // 获取关联的配对记录列表
        List<SessionMatchRecordDO> sessionMatchRecordDOS = sessionMatchRecordService.getSessionMatchRecordList(id);
        sessionRespVO.setMatchRecords(sessionMatchRecordDOS);
        return success(sessionRespVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得学习会话分页")
    @PreAuthorize("@ss.hasPermission('learning:session:query')")
    public CommonResult<PageResult<SessionRespVO>> getSessionPage(@Valid SessionPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<SessionDTO> pageResult = sessionService.getSessionPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, SessionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出学习会话 Excel")
    @PreAuthorize("@ss.hasPermission('learning:session:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSessionExcel(@Valid SessionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SessionDTO> list = sessionService.getSessionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "学习会话.xls", "数据", SessionRespVO.class,
                        BeanUtils.toBean(list, SessionRespVO.class));
    }

}