package com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 题目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionBankPageReqVO extends PageParam {

    @Schema(description = "AI出题ID", example = "5445")
    private Long aiQuestionId;

    @Schema(description = "题目类型", example = "2")
    private Integer questionType;

    @Schema(description = "用户ID", example = "6522")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

    @Schema(description = "创建方式", example = "1")
    private Integer createMethod;

    @Schema(description = "是否公开", example = "1")
    private Boolean publicStatus;

    @Schema(description = "题干")
    private String questionStem;

    @Schema(description = "参考答案")
    private String answer;

    @Schema(description = "题目出处")
    private String source;

    @Schema(description = "试题解析")
    private String analysis;

    @Schema(description = "难易度")
    private Integer difficulty;

    @Schema(description = "未通过原因", example = "不好")
    private String rejectReason;

    @Schema(description = "审核状态", example = "1")
    private Integer auditStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}