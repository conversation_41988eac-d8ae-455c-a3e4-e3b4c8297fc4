package com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 配对记录新增/修改 Request VO")
@Data
public class SessionMatchRecordSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23269")
    private Long id;

    @Schema(description = "学习集ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30813")
    @NotNull(message = "学习集ID不能为空")
    private Long setId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30867")
    private Long userId;

    @Schema(description = "学习会话ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6221")
    @NotNull(message = "学习会话ID不能为空")
    private Long sessionId;

    @Schema(description = "词语", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "词语不能为空")
    private String word;

    @Schema(description = "定义", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "定义不能为空")
    private String definition;

    @Schema(description = "是否正确")
    private Integer isCorrect;

}