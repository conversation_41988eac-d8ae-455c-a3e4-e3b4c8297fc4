package com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 学习集置顶新增/修改 Request VO")
@Data
public class WordSetTopSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1380")
    private Long id;

    @Schema(description = "学习集编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30750")
    @NotNull(message = "学习集编号不能为空")
    private Long setId;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "16074")
    private Long userId;

}