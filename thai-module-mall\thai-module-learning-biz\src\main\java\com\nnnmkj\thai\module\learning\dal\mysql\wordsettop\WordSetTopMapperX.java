package com.nnnmkj.thai.module.learning.dal.mysql.wordsettop;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习集置顶 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordSetTopMapperX extends BaseMapperX<WordSetTopDTO> {

    default PageResult<WordSetTopDTO> selectPage(WordSetTopPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<WordSetTopDTO> wrapper = new MPJLambdaWrapperX<WordSetTopDTO>()
                .selectAll(WordSetTopDO.class)
                .selectAs("t1.nickname", WordSetTopDTO::getNickname)
                .selectAs(WordSetDO::getTitle, WordSetTopDTO::getSetTitle)
                .likeIfPresent(WordSetTopDO::getCreator, creatorQuery)
                .eqIfPresent(WordSetTopDO::getSetId, reqVO.getSetId())
                .betweenIfPresent(WordSetTopDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordSetTopDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, WordSetTopDO::getSetId);
        return selectPage(reqVO, wrapper);
    }

}