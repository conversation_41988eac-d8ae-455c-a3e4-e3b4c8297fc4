package com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课程作业发布题目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AssignmentReleaseQuestionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7418")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32557")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "发布ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27852")
    @ExcelProperty("发布ID")
    private Long assignmentReleaseId;

    @Schema(description = "题目类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("题目类型")
    private Integer questionType;

    @Schema(description = "题干", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("题干")
    private String questionStem;

    @Schema(description = "参考答案", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("参考答案")
    private String answer;

    @Schema(description = "题目出处", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("题目出处")
    private String source;

    @Schema(description = "试题解析", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("试题解析")
    private String analysis;

    @Schema(description = "难易度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("难易度")
    private Integer difficulty;

    @Schema(description = "分数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分数")
    private Double score;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}