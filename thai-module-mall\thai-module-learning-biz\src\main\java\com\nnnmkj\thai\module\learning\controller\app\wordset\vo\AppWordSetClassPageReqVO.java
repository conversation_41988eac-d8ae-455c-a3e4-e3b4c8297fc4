package com.nnnmkj.thai.module.learning.controller.app.wordset.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "用户 APP - 班级下的学习集分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppWordSetClassPageReqVO extends PageParam {

    @Schema(description = "班级ID")
    @NotEmpty(message = "班级ID不能为空")
    private String classId;

    @Schema(description = "用户ID", example = "1024")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

    @Schema(description = "状态 0:教师创建 1：用户创建", example = "0")
    private Integer status;
}