package com.nnnmkj.thai.module.phonetic.controller.app.pronunciationaccessment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户APP - 发音评估 Response VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppPronunciationAssessmentRespVO {

    @Schema(description = "是否成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean success;

    @Schema(description = "准确度得分", requiredMode = Schema.RequiredMode.REQUIRED, example = "85.5")
    private Double accuracyScore;

    @Schema(description = "发音得分", requiredMode = Schema.RequiredMode.REQUIRED, example = "90.2")
    private Double pronunciationScore;

    @Schema(description = "完整度得分", requiredMode = Schema.RequiredMode.REQUIRED, example = "95.0")
    private Double completenessScore;

    @Schema(description = "流利度得分", requiredMode = Schema.RequiredMode.REQUIRED, example = "88.7")
    private Double fluencyScore;

    // @Schema(description = "韵律得分", requiredMode = Schema.RequiredMode.REQUIRED, example = "82.3")
    // private Double prosodyScore;

    @Schema(description = "识别的文本", requiredMode = Schema.RequiredMode.REQUIRED, example = "Hello world")
    private String recognizedText;
    
    @Schema(description = "音频文件URL", example = "https://example.com/audio/123.wav")
    private String audioUrl;
}
