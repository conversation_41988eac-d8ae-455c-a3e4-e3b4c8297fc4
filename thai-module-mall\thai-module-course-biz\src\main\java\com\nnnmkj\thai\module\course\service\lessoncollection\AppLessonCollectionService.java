package com.nnnmkj.thai.module.course.service.lessoncollection;

import com.nnnmkj.thai.module.course.controller.app.lessoncollection.vo.AppLessonCollectionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 课程收藏 Service 接口 (APP)
 *
 * <AUTHOR>
 */
public interface AppLessonCollectionService {

    /**
     * 创建课程收藏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLessonCollection(@Valid AppLessonCollectionSaveReqVO createReqVO);

    /**
     * 删除课程收藏
     *
     * @param lessonId 课程编号
     * @param userId 用户编号
     */
    void deleteLessonCollection(Long lessonId, Long userId);

    /**
     * 获得课程收藏列表
     *
     * @param lessonIds 课程编号集合
     * @param userId 用户编号
     * @return 课程收藏列表
     */
    List<LessonCollectionDO> getLessonCollectionList(Collection<Long> lessonIds, Long userId);

    /**
     * 获得用户收藏的课程列表
     *
     * @param userId 用户编号
     * @return 课程收藏列表
     */
    List<LessonCollectionDO> getLessonCollectionListByUserId(Long userId);

    /**
     * 检查用户是否收藏了指定课程
     *
     * @param lessonId 课程编号
     * @param userId 用户编号
     * @param userType 用户类型
     * @return 是否收藏
     */
    boolean isUserCollectedLesson(Long lessonId, Long userId, Integer userType);
} 