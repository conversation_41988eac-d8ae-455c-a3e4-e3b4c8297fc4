package com.nnnmkj.thai.module.course.controller.admin.lesson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 课程精简 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonSimpleRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "6445")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("课程标题")
    private String title;

}