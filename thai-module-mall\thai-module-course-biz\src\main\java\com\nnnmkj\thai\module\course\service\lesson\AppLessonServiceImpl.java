package com.nnnmkj.thai.module.course.service.lesson;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonFavoritePageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonSaveReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonSearchReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergrouplesson.MemberGroupLessonDO;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonAttachmentMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonMapper;
import com.nnnmkj.thai.module.course.service.lessoncollection.AppLessonCollectionService;
import com.nnnmkj.thai.module.course.service.membergrouplesson.MemberGroupLessonService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_ATTACHMENT_NOT_EXISTS;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_NOT_EXISTS;

/**
 * 课程 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppLessonServiceImpl implements AppLessonService {

    @Resource
    private LessonMapper lessonMapper;
    @Resource
    private LessonAttachmentMapper lessonAttachmentMapper;
    @Resource
    private AppLessonCollectionService lessonCollectionService;
    @Resource
    private MemberGroupLessonService memberGroupLessonService;
    @Resource
    private MemberUserApi memberUserApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLesson(AppLessonSaveReqVO createReqVO) {
        // 插入
        LessonDO lesson = BeanUtils.toBean(createReqVO, LessonDO.class);
        lessonMapper.insert(lesson);

        // 插入子表
        createLessonAttachmentList(lesson.getId(), createReqVO.getLessonAttachments());
        // 返回
        return lesson.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLesson(AppLessonSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonExists(updateReqVO.getId());
        // 更新
        LessonDO updateObj = BeanUtils.toBean(updateReqVO, LessonDO.class);
        lessonMapper.updateById(updateObj);

        // 更新子表
        updateLessonAttachmentList(updateReqVO.getId(), updateReqVO.getLessonAttachments());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLesson(Long id) {
        // 校验存在
        validateLessonExists(id);
        // 删除
        lessonMapper.deleteById(id);

        // 删除子表
        deleteLessonAttachmentByCourseId(id);
    }

    private void validateLessonExists(Long id) {
        if (lessonMapper.selectById(id) == null) {
            throw exception(LESSON_NOT_EXISTS);
        }
    }


    @Override
    public LessonDO getLesson(Long id) {
        return lessonMapper.selectById(id);
    }

    @Override
    public PageResult<LessonDO> getLessonPage(AppLessonPageReqVO pageReqVO) {
        return lessonMapper.selectPage(pageReqVO);
    }

    @Override
    public List<LessonDO> getLessonList(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return lessonMapper.selectList(new LambdaQueryWrapperX<LessonDO>().in(LessonDO::getId, ids));
    }

    @Override
    public List<LessonDO> getLessonListByIds(Set<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return lessonMapper.selectList(new LambdaQueryWrapperX<LessonDO>().in(LessonDO::getId, ids));
    }

    @Override
    public List<LessonDO> getLessonListByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        String creatorQuery = CommonUtils.getCreatorQuery(userId, WebFrameworkUtils.getLoginUserType());
        if (!StringUtils.hasText(creatorQuery)) {
            return new ArrayList<>();
        }
        return lessonMapper.selectList(new LambdaQueryWrapperX<LessonDO>()
                .likeIfPresent(LessonDO::getCreator, creatorQuery)
                .orderByDesc(LessonDO::getCreateTime));
    }

    // ==================== 子表（课程附件） ====================

    @Override
    public List<LessonAttachmentDO> getLessonAttachmentListByCourseId(Long courseId) {
        return lessonAttachmentMapper.selectListByCourseId(courseId);
    }

    @Override
    public List<LessonAttachmentDO> searchLessonAttachmentsByTitle(String keyword, Long courseId) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return lessonAttachmentMapper.selectList(
                new LambdaQueryWrapperX<LessonAttachmentDO>()
                        .eq(courseId != null, LessonAttachmentDO::getCourseId, courseId)
                        .like(LessonAttachmentDO::getTitle, keyword)
                        .orderByDesc(LessonAttachmentDO::getId));
    }

    private void createLessonAttachmentList(Long courseId, List<LessonAttachmentDO> list) {
        list.forEach(o -> o.setCourseId(courseId));
        lessonAttachmentMapper.insertBatch(list);
    }

    private void updateLessonAttachmentList(Long courseId, List<LessonAttachmentDO> list) {
        deleteLessonAttachmentByCourseId(courseId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createLessonAttachmentList(courseId, list);
    }

    private void deleteLessonAttachmentByCourseId(Long courseId) {
        lessonAttachmentMapper.deleteByCourseId(courseId);
    }

    @Override
    public PageResult<LessonDO> getFavoriteLessonPage(AppLessonFavoritePageReqVO pageReqVO) {
        // 1. 获取当前用户收藏的课程列表
        List<LessonCollectionDO> lessonCollections = lessonCollectionService.getLessonCollectionListByUserId(pageReqVO.getUserId());
        if (CollUtil.isEmpty(lessonCollections)) {
            return PageResult.empty();
        }
        
        // 2. 获取收藏的课程ID集合
        Set<Long> lessonIds = lessonCollections.stream()
                .map(LessonCollectionDO::getLessonId)
                .collect(Collectors.toSet());
        
        // 3. 查询课程信息
        return lessonMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<LessonDO>()
                .in(LessonDO::getId, lessonIds)
                .likeIfPresent(LessonDO::getTitle, pageReqVO.getTitle())
                .orderByDesc(LessonDO::getCreateTime));
    }

    @Override
    public PageResult<LessonDO> searchLessons(AppLessonSearchReqVO reqVO, Long userId) {
        if (userId == null) {
            return PageResult.empty();
        }
        
        List<LessonDO> lessonList = new ArrayList<>();
        long total = 0;
        
        // 1. 根据搜索类型获取课程列表
        if (reqVO.getType() == 1) {
            // 我创建的课程
            lessonList = searchCreatedLessons(userId, reqVO);
            total = lessonList.size();
            
            // 手动分页处理
            int fromIndex = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
            if (fromIndex >= lessonList.size()) {
                return PageResult.empty();
            }
            int toIndex = Math.min(fromIndex + reqVO.getPageSize(), lessonList.size());
            lessonList = lessonList.subList(fromIndex, toIndex);
        } else if (reqVO.getType() == 2) {
            // 我收藏的课程
            AppLessonFavoritePageReqVO pageReqVO = new AppLessonFavoritePageReqVO();
            pageReqVO.setUserId(userId);
            pageReqVO.setTitle(reqVO.getKeyword());
            pageReqVO.setPageNo(reqVO.getPageNo());
            pageReqVO.setPageSize(reqVO.getPageSize());
            
            PageResult<LessonDO> pageResult = getFavoriteLessonPage(pageReqVO);
            lessonList = pageResult.getList();
            total = pageResult.getTotal();
        } else if (reqVO.getType() == 3) {
            // 我加入的课程
            lessonList = searchJoinedLessons(userId, reqVO);
            total = lessonList.size();
            
            // 手动分页处理
            int fromIndex = (reqVO.getPageNo() - 1) * reqVO.getPageSize();
            if (fromIndex >= lessonList.size()) {
                return PageResult.empty();
            }
            int toIndex = Math.min(fromIndex + reqVO.getPageSize(), lessonList.size());
            lessonList = lessonList.subList(fromIndex, toIndex);
        }
        
        return new PageResult<>(lessonList, total);
    }
    
    /**
     * 搜索我创建的课程
     */
    private List<LessonDO> searchCreatedLessons(Long userId, AppLessonSearchReqVO reqVO) {
        List<LessonDO> lessons = getLessonListByUserId(userId);
        if (CollUtil.isEmpty(lessons)) {
            return Collections.emptyList();
        }
        
        // 关键词过滤
        if (StringUtils.hasText(reqVO.getKeyword())) {
            String keyword = reqVO.getKeyword().toLowerCase();
            lessons = lessons.stream()
                    .filter(lesson -> {
                        // 标题或描述包含关键词
                        return StringUtils.hasText(lesson.getTitle()) && lesson.getTitle().toLowerCase().contains(keyword) ||
                               StringUtils.hasText(lesson.getDescription()) && lesson.getDescription().toLowerCase().contains(keyword);
                    })
                    .collect(Collectors.toList());
        }
        
        return lessons;
    }
    
    /**
     * 搜索我加入的课程
     */
    private List<LessonDO> searchJoinedLessons(Long userId, AppLessonSearchReqVO reqVO) {
        // 1. 获取用户加入的班级列表
        List<Long> groupIds = memberUserApi.getUserGroups(userId);
        if (CollUtil.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        
        // 2. 获取班级关联的课程ID集合
        Set<Long> courseIds = CollUtil.newHashSet();
        for (Long groupId : groupIds) {
            List<MemberGroupLessonDO> memberGroupLessons = memberGroupLessonService.getMemberGroupLessonListByGroupId(groupId);
            if (CollUtil.isNotEmpty(memberGroupLessons)) {
                Set<Long> ids = memberGroupLessons.stream()
                        .map(MemberGroupLessonDO::getCourseId)
                        .collect(Collectors.toSet());
                courseIds.addAll(ids);
            }
        }
        
        if (CollUtil.isEmpty(courseIds)) {
            return Collections.emptyList();
        }
        
        // 3. 查询课程信息
        List<LessonDO> lessons = getLessonListByIds(courseIds);
        if (CollUtil.isEmpty(lessons)) {
            return Collections.emptyList();
        }
        
        // 4. 关键词过滤
        if (StringUtils.hasText(reqVO.getKeyword())) {
            String keyword = reqVO.getKeyword().toLowerCase();
            lessons = lessons.stream()
                    .filter(lesson -> {
                        // 标题或描述包含关键词
                        return StringUtils.hasText(lesson.getTitle()) && lesson.getTitle().toLowerCase().contains(keyword) ||
                               StringUtils.hasText(lesson.getDescription()) && lesson.getDescription().toLowerCase().contains(keyword);
                    })
                    .collect(Collectors.toList());
        }
        
        return lessons;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLessonAttachmentById(Long id) {
        validateLessonAttachmentExists(id);
        //根据课程附件表删除附件
        lessonAttachmentMapper.deleteById(id);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLessonAttachmentTitle(long id, String title) {
        validateLessonAttachmentExists(id);
        lessonAttachmentMapper.updateTitleById(id,"title",title);
    }

    //验证课程附件是否存在
    private void validateLessonAttachmentExists(Long id) {
        if (lessonAttachmentMapper.selectById(id) == null) {
            throw exception(LESSON_ATTACHMENT_NOT_EXISTS);
        }
    }


}