package com.nnnmkj.thai.module.quiz.dal.mysql.questionBank;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo.QuestionBankPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.app.questionBank.vo.AppQuestionBankPageReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 题目 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionBankMapper extends BaseMapperX<QuestionBankDO> {

    default PageResult<QuestionBankDO> selectPage(QuestionBankPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionBankDO>()
                .eqIfPresent(QuestionBankDO::getAiQuestionId, reqVO.getAiQuestionId())
                .eqIfPresent(QuestionBankDO::getQuestionType, reqVO.getQuestionType())
                .eqIfPresent(QuestionBankDO::getCreateMethod, reqVO.getCreateMethod())
                .likeIfPresent(QuestionBankDO::getQuestionStem, reqVO.getQuestionStem())
                .likeIfPresent(QuestionBankDO::getAnswer, reqVO.getAnswer())
                .likeIfPresent(QuestionBankDO::getSource, reqVO.getSource())
                .likeIfPresent(QuestionBankDO::getAnalysis, reqVO.getAnalysis())
                .eqIfPresent(QuestionBankDO::getDifficulty, reqVO.getDifficulty())
                .likeIfPresent(QuestionBankDO::getRejectReason, reqVO.getRejectReason())
                .eqIfPresent(QuestionBankDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(QuestionBankDO::getPublicStatus, reqVO.getPublicStatus())
                .likeIfPresent(QuestionBankDO::getCreator, creatorQuery)
                .betweenIfPresent(QuestionBankDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionBankDO::getId));
    }

    default PageResult<QuestionBankDO> selectPage(AppQuestionBankPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionBankDO>()
                .eqIfPresent(QuestionBankDO::getAiQuestionId, reqVO.getAiQuestionId())
                .eqIfPresent(QuestionBankDO::getQuestionType, reqVO.getQuestionType())
                .likeIfPresent(QuestionBankDO::getQuestionStem, reqVO.getQuestionStem())
                .likeIfPresent(QuestionBankDO::getAnswer, reqVO.getAnswer())
                .likeIfPresent(QuestionBankDO::getSource, reqVO.getSource())
                .likeIfPresent(QuestionBankDO::getAnalysis, reqVO.getAnalysis())
                .eqIfPresent(QuestionBankDO::getDifficulty, reqVO.getDifficulty())
                .likeIfPresent(QuestionBankDO::getRejectReason, reqVO.getRejectReason())
                .eqIfPresent(QuestionBankDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(QuestionBankDO::getPublicStatus, reqVO.getPublicStatus())
                .likeIfPresent(QuestionBankDO::getCreator, creatorQuery)
                .betweenIfPresent(QuestionBankDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionBankDO::getId));
    }

    @Update("UPDATE quiz_question_bank SET user_id = NULL WHERE id = ${id};")
    int clearUserIdById(@Param("id") Long id);

}