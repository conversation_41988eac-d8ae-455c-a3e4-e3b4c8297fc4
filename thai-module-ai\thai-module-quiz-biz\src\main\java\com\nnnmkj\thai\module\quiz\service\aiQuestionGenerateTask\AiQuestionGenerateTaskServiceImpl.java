package com.nnnmkj.thai.module.quiz.service.aiQuestionGenerateTask;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskSaveReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskRecordDO;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiQuestionGenerateTask.AiQuestionGenerateTaskMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiQuestionGenerateTask.AiQuestionGenerateTaskMapperX;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiQuestionGenerateTask.AiQuestionGenerateTaskRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.quiz.enums.ErrorCodeConstants.AI_QUESTION_GENERATE_TASK_NOT_EXISTS;

/**
 * AI出题生成任务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AiQuestionGenerateTaskServiceImpl implements AiQuestionGenerateTaskService {

    @Resource
    private AiQuestionGenerateTaskMapper aiQuestionGenerateTaskMapper;
    @Resource
    private AiQuestionGenerateTaskMapperX aiQuestionGenerateTaskMapperX;
    @Resource
    private AiQuestionGenerateTaskRecordMapper aiQuestionGenerateTaskRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAiQuestionGenerateTask(AiQuestionGenerateTaskSaveReqVO createReqVO) {
        // 插入
        AiQuestionGenerateTaskDO aiQuestionGenerateTask = BeanUtils.toBean(createReqVO, AiQuestionGenerateTaskDO.class);
        aiQuestionGenerateTaskMapper.insert(aiQuestionGenerateTask);

        // 插入子表
        List<AiQuestionGenerateTaskRecordDO> taskRecords = createReqVO.getAiQuestionGenerateTaskRecords();
        if (CollUtil.isNotEmpty(taskRecords)) {
            createAiQuestionGenerateTaskRecordList(aiQuestionGenerateTask.getId(), taskRecords);
        }
        // 返回
        return aiQuestionGenerateTask.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAiQuestionGenerateTask(AiQuestionGenerateTaskSaveReqVO updateReqVO) {
        // 校验存在
        validateAiQuestionGenerateTaskExists(updateReqVO.getId());
        // 更新
        AiQuestionGenerateTaskDO updateObj = BeanUtils.toBean(updateReqVO, AiQuestionGenerateTaskDO.class);
        aiQuestionGenerateTaskMapper.updateById(updateObj);

        // 更新子表
        List<AiQuestionGenerateTaskRecordDO> taskRecords = updateReqVO.getAiQuestionGenerateTaskRecords();
        if (CollUtil.isNotEmpty(taskRecords)) {
            updateAiQuestionGenerateTaskRecordList(updateReqVO.getId(), taskRecords);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAiQuestionGenerateTask(Long id) {
        // 校验存在
        validateAiQuestionGenerateTaskExists(id);
        // 删除
        aiQuestionGenerateTaskMapper.deleteById(id);

        // 删除子表
        deleteAiQuestionGenerateTaskRecordByTaskId(id);
    }

    private void validateAiQuestionGenerateTaskExists(Long id) {
        if (aiQuestionGenerateTaskMapper.selectById(id) == null) {
            throw exception(AI_QUESTION_GENERATE_TASK_NOT_EXISTS);
        }
    }

    @Override
    public AiQuestionGenerateTaskDO getAiQuestionGenerateTask(Long id) {
        return aiQuestionGenerateTaskMapper.selectById(id);
    }

    @Override
    public PageResult<AiQuestionGenerateTaskDTO> getAiQuestionGenerateTaskPage(AiQuestionGenerateTaskPageReqVO pageReqVO) {
        return aiQuestionGenerateTaskMapperX.selectPage(pageReqVO);
    }

    // ==================== 子表（AI出题生成任务记录） ====================

    @Override
    public List<AiQuestionGenerateTaskRecordDO> getAiQuestionGenerateTaskRecordListByTaskId(Long taskId) {
        return aiQuestionGenerateTaskRecordMapper.selectListByTaskId(taskId);
    }

    private void createAiQuestionGenerateTaskRecordList(Long taskId, List<AiQuestionGenerateTaskRecordDO> list) {
        list.forEach(o -> o.setTaskId(taskId));
        aiQuestionGenerateTaskRecordMapper.insertBatch(list);
    }

    private void updateAiQuestionGenerateTaskRecordList(Long taskId, List<AiQuestionGenerateTaskRecordDO> list) {
        deleteAiQuestionGenerateTaskRecordByTaskId(taskId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createAiQuestionGenerateTaskRecordList(taskId, list);
    }

    private void deleteAiQuestionGenerateTaskRecordByTaskId(Long taskId) {
        aiQuestionGenerateTaskRecordMapper.deleteByTaskId(taskId);
    }

}