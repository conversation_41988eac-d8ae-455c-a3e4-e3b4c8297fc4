package com.nnnmkj.thai.module.course.dal.mysql.assignment;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDTO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程作业 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssignmentMapperX extends BaseMapperX<AssignmentDTO> {

    default PageResult<AssignmentDTO> selectPage(AssignmentPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), WebFrameworkUtils.getLoginUserType());
        MPJLambdaWrapper<AssignmentDTO> wrapper = new MPJLambdaWrapperX<AssignmentDTO>()
                .selectAll(AssignmentDO.class)
                .selectAs("t1.nickname", AssignmentDTO::getNickname)
                .selectAs(LessonDO::getTitle, AssignmentDTO::getCourseTitle)
                .eqIfPresent(AssignmentDO::getCourseId, reqVO.getCourseId())
                .likeIfPresent(AssignmentDO::getName, reqVO.getName())
                .eqIfPresent(AssignmentDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(AssignmentDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(AssignmentDO::getCreator, creatorQuery)
                .orderByDesc(AssignmentDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(LessonDO.class, "t2", LessonDO::getId, AssignmentDO::getCourseId);
        return selectPage(reqVO, wrapper);
    }
}