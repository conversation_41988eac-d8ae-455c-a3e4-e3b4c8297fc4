package com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord;

import com.nnnmkj.thai.module.learning.dal.dataobject.wordcarddefinition.WordCardDefinitionDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 单词卡记录 DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SessionCardRecordDTO extends SessionCardRecordDO {

    /**
     * 单词
     */
    private String word;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 学习集标题
     */
    private String setTitle;
    /**
     * 单词定义
     */
    private List<WordCardDefinitionDO> definitions;


}