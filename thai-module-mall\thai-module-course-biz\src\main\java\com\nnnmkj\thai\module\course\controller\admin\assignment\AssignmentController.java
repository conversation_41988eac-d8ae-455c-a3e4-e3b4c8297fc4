package com.nnnmkj.thai.module.course.controller.admin.assignment;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentRespVO;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDTO;
import com.nnnmkj.thai.module.course.service.assignment.AssignmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程作业")
@RestController
@RequestMapping("/course/assignment")
@Validated
public class AssignmentController {

    @Resource
    private AssignmentService assignmentService;

    @PostMapping("/create")
    @Operation(summary = "创建课程作业")
    @PreAuthorize("@ss.hasPermission('course:assignment:create')")
    public CommonResult<Long> createAssignment(@Valid @RequestBody AssignmentSaveReqVO createReqVO) {
        createReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        return success(assignmentService.createAssignment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业")
    @PreAuthorize("@ss.hasPermission('course:assignment:update')")
    public CommonResult<Boolean> updateAssignment(@Valid @RequestBody AssignmentSaveReqVO updateReqVO) {
        updateReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        assignmentService.updateAssignment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:assignment:delete')")
    public CommonResult<Boolean> deleteAssignment(@RequestParam("id") Long id) {
        assignmentService.deleteAssignment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程作业")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:assignment:query')")
    public CommonResult<AssignmentRespVO> getAssignment(@RequestParam("id") Long id) {
        AssignmentDO assignment = assignmentService.getAssignment(id);
        return success(BeanUtils.toBean(assignment, AssignmentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程作业分页")
    @PreAuthorize("@ss.hasPermission('course:assignment:query')")
    public CommonResult<PageResult<AssignmentRespVO>> getAssignmentPage(@Valid AssignmentPageReqVO pageReqVO) {
        PageResult<AssignmentDTO> pageResult = assignmentService.getAssignmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssignmentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程作业 Excel")
    @PreAuthorize("@ss.hasPermission('course:assignment:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAssignmentExcel(@Valid AssignmentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssignmentDTO> list = assignmentService.getAssignmentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程作业.xls", "数据", AssignmentRespVO.class,
                        BeanUtils.toBean(list, AssignmentRespVO.class));
    }

}