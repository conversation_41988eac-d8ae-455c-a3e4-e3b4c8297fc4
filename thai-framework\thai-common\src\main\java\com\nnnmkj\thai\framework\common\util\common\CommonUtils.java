package com.nnnmkj.thai.framework.common.util.common;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.nnnmkj.thai.framework.common.dataobject.BaseEntity;
import com.nnnmkj.thai.framework.common.enums.CommonConstants;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 通用工具类
 */
public class CommonUtils {

    /**
     * 根据用户类型拼接creator查询条件
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 拼接后的 creator查询条件
     */
    public static String getCreatorQuery(Long userId, Integer userType) {
        if (Objects.isNull(userId) || Objects.isNull(userType)) {
            return "";
        }
        UserTypeEnum typeEnum = UserTypeEnum.valueOf(userType);
        if (Objects.isNull(typeEnum)) {
            return "";
        }
        if (typeEnum == UserTypeEnum.MEMBER) {
            return CommonConstants.MEMBER_USER_ID_PREFIX + userId + CommonConstants.USER_ID_SUFFIX;
        } else if (typeEnum == UserTypeEnum.ADMIN) {
            return CommonConstants.SYSTEM_USER_ID_PREFIX + userId + CommonConstants.USER_ID_SUFFIX;
        } else {
            return "";
        }
    }

    /**
     * 根据用户类型枚举拼接creator查询条件
     *
     * @param userId 用户ID
     * @param userTypeEnum 用户类型枚举 {@link UserTypeEnum}
     * @return 拼接后的 creator查询条件
     */
    public static String getCreatorQuery(Long userId, UserTypeEnum userTypeEnum) {
        return getCreatorQuery(userId, userTypeEnum.getValue());
    }

    /**
     * 根据用户类型拼接LIKE查询的 creator 条件
     *
     * @param userId 用户ID
     * @param userType 用户类型 {@link UserTypeEnum}
     * @return 拼接后的 LIKE 查询 creator 条件
     */
    public static String getLikeCreatorQuery(Long userId, Integer userType) {
        return CommonConstants.PERCENTAGE + getCreatorQuery(userId, userType) + CommonConstants.PERCENTAGE;
    }

    /**
     * 从 creator 查询 SQL 中提取用户ID
     *
     * @param creatorQuery creator 查询条件字符串
     * @param userType 用户类型 {@link UserTypeEnum}
     * @return 提取到的用户ID，如果解析失败或参数无效则返回 null
     */
    public static Long getUserIdByCreatorQuery(String creatorQuery, Integer userType) {
        if (StrUtil.isBlank(creatorQuery) || Objects.isNull(userType)) {
            return null;
        }
        if (NumberUtil.isNumber(creatorQuery)) {
            return Long.valueOf(creatorQuery);
        }
        UserTypeEnum typeEnum = UserTypeEnum.valueOf(userType);
        if (Objects.isNull(typeEnum)) {
            return null;
        }

        // 根据用户类型确定前缀
        String prefix = switch (typeEnum) {
            case ADMIN -> CommonConstants.SYSTEM_USER_ID_PREFIX;
            case MEMBER -> CommonConstants.MEMBER_USER_ID_PREFIX;
        };

        // 前缀为空表示不支持的用户类型
        if (StrUtil.isBlank(prefix)) {
            return null;
        }

        // 构建目标标识符（例如：su_id:123| 或 mu_id:456|）
        String targetIdWithBoundaries = StrUtil.split(creatorQuery, CommonConstants.USER_ID_SUFFIX)
                .stream()
                .filter(s -> s.startsWith(prefix))
                .findFirst()
                .orElse(null);

        if (StrUtil.isBlank(targetIdWithBoundaries)) {
            return null;
        }

        String userIdStr = targetIdWithBoundaries.replaceFirst(prefix, "");

        // 验证是否为有效数字
        if (StrUtil.isBlank(userIdStr) || !NumberUtil.isNumber(userIdStr)) {
            return null;
        }

        return Long.valueOf(userIdStr);
    }

    /**
     * 从 creator 查询 SQL 中提取用户ID
     *
     * @param creatorQuery creator 查询条件字符串
     * @param userTypeEnum 用户类型枚举 {@link UserTypeEnum}
     * @return 提取到的用户ID，如果解析失败或参数无效则返回 null
     */
    public static Long getUserIdByCreatorQuery(String creatorQuery, UserTypeEnum userTypeEnum) {
        return getUserIdByCreatorQuery(creatorQuery, userTypeEnum.getValue());
    }

    /**
     * 从基础实体获取用户ID
     *
     * @param entity 基础实体对象
     * @param userTypeEnum 用户类型
     * @return 用户ID
     */
    public static <T extends BaseEntity> Long getUserIdByBaseDO(T entity, UserTypeEnum userTypeEnum) {
        return getUserIdByCreatorQuery(entity.getCreator(), userTypeEnum);
    }

    /**
     * 从多个基础实体获取用户ID列表
     *
     * @param entities 多个基础实体对象
     * @param userTypeEnum 用户类型
     * @return 用户ID列表
     */
    public static <T extends BaseEntity> List<Long> getUserIdsByBaseDOS(Collection<T> entities, UserTypeEnum userTypeEnum) {
        return CollectionUtils.convertList(entities, entity -> getUserIdByCreatorQuery(entity.getCreator(), userTypeEnum));
    }
}
