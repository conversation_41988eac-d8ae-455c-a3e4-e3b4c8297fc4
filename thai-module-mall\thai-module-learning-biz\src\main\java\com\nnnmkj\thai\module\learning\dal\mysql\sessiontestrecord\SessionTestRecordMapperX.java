package com.nnnmkj.thai.module.learning.dal.mysql.sessiontestrecord;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测试记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionTestRecordMapperX extends BaseMapperX<SessionTestRecordDTO> {

    default PageResult<SessionTestRecordDTO> selectPage(SessionTestRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<SessionTestRecordDTO> wrapper = new MPJLambdaWrapperX<SessionTestRecordDTO>()
                .selectAll(SessionTestRecordDO.class)
                .selectAs("t1.nickname", SessionTestRecordDTO::getNickname)
                .selectAs(WordSetDO::getTitle, SessionTestRecordDTO::getSetTitle)
                .eqIfPresent(SessionTestRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionTestRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionTestRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionTestRecordDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(SessionTestRecordDO::getUserAnswer, reqVO.getUserAnswer())
                .eqIfPresent(SessionTestRecordDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(SessionTestRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionTestRecordDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, SessionTestRecordDO::getSetId);
        return selectPage(reqVO, wrapper);
    }

}