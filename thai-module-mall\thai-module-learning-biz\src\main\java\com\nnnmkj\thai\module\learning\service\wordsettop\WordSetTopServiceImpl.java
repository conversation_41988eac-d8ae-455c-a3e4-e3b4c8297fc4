package com.nnnmkj.thai.module.learning.service.wordsettop;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsettop.WordSetTopMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsettop.WordSetTopMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.WORD_SET_TOP_NOT_EXISTS;

/**
 * 学习集置顶 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordSetTopServiceImpl implements WordSetTopService {

    @Resource
    private WordSetTopMapper wordSetTopMapper;
    @Resource
    private WordSetTopMapperX wordSetTopMapperX;

    @Override
    public Long createWordSetTop(WordSetTopSaveReqVO createReqVO) {
        // 插入
        WordSetTopDO wordSetTop = BeanUtils.toBean(createReqVO, WordSetTopDO.class);
        wordSetTopMapper.insert(wordSetTop);
        // 返回
        return wordSetTop.getId();
    }

    @Override
    public void updateWordSetTop(WordSetTopSaveReqVO updateReqVO) {
        // 校验存在
        validateWordSetTopExists(updateReqVO.getId());
        // 更新
        WordSetTopDO updateObj = BeanUtils.toBean(updateReqVO, WordSetTopDO.class);
        wordSetTopMapper.updateById(updateObj);
    }

    @Override
    public void deleteWordSetTop(Long id) {
        // 校验存在
        validateWordSetTopExists(id);
        // 删除
        wordSetTopMapper.deleteById(id);
    }

    private void validateWordSetTopExists(Long id) {
        if (wordSetTopMapper.selectById(id) == null) {
            throw exception(WORD_SET_TOP_NOT_EXISTS);
        }
    }

    @Override
    public WordSetTopDO getWordSetTop(Long id) {
        return wordSetTopMapper.selectById(id);
    }

    @Override
    public PageResult<WordSetTopDTO> getWordSetTopPage(WordSetTopPageReqVO pageReqVO) {
        return wordSetTopMapperX.selectPage(pageReqVO);
    }

}