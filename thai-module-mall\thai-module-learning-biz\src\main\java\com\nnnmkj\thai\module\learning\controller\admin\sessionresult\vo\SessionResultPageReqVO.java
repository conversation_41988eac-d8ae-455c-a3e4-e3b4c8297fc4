package com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会话成绩分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SessionResultPageReqVO extends PageParam {

    @Schema(description = "学习会话ID", example = "7617")
    private Long sessionId;

    @Schema(description = "用户ID", example = "15958")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

    @Schema(description = "学习集ID", example = "12795")
    private Long setId;

    @Schema(description = "完成时间(秒)")
    private Integer timeTaken;

    @Schema(description = "总题目数", example = "11954")
    private Integer allCount;

    @Schema(description = "答对数量", example = "26740")
    private Integer correctCount;

    @Schema(description = "答错数量", example = "21480")
    private Integer errorCount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}