package com.nnnmkj.thai.module.course.dal.mysql.assignmentquestion;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程作业题目 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssignmentQuestionMapper extends BaseMapperX<AssignmentQuestionDO> {

    default PageResult<AssignmentQuestionDO> selectPage(AssignmentQuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssignmentQuestionDO>()
                .eqIfPresent(AssignmentQuestionDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(AssignmentQuestionDO::getAssignmentId, reqVO.getAssignmentId())
                .eqIfPresent(AssignmentQuestionDO::getQuestionType, reqVO.getQuestionType())
                .eqIfPresent(AssignmentQuestionDO::getDifficulty, reqVO.getDifficulty())
                .betweenIfPresent(AssignmentQuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AssignmentQuestionDO::getId));
    }

}