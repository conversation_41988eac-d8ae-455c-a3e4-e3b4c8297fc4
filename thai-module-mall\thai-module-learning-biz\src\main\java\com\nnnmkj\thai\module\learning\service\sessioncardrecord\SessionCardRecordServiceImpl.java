package com.nnnmkj.thai.module.learning.service.sessioncardrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.sessioncardrecord.SessionCardRecordMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.sessioncardrecord.SessionCardRecordMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.SESSION_CARD_RECORD_NOT_EXISTS;

/**
 * 单词卡记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SessionCardRecordServiceImpl implements SessionCardRecordService {

    @Resource
    private SessionCardRecordMapper sessionCardRecordMapper;

    @Resource
    private SessionCardRecordMapperX sessionCardRecordMapperX;

    @Override
    public Long createSessionCardRecord(SessionCardRecordSaveReqVO createReqVO) {
        // 插入
        SessionCardRecordDO sessionCardRecord = BeanUtils.toBean(createReqVO, SessionCardRecordDO.class);
        sessionCardRecordMapper.insert(sessionCardRecord);
        // 返回
        return sessionCardRecord.getId();
    }

    @Override
    public void updateSessionCardRecord(SessionCardRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateSessionCardRecordExists(updateReqVO.getId());
        // 更新
        SessionCardRecordDO updateObj = BeanUtils.toBean(updateReqVO, SessionCardRecordDO.class);
        sessionCardRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteSessionCardRecord(Long id) {
        // 校验存在
        validateSessionCardRecordExists(id);
        // 删除
        sessionCardRecordMapper.deleteById(id);
    }

    private void validateSessionCardRecordExists(Long id) {
        if (sessionCardRecordMapper.selectById(id) == null) {
            throw exception(SESSION_CARD_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public SessionCardRecordDO getSessionCardRecord(Long id) {
        return sessionCardRecordMapper.selectById(id);
    }

    @Override
    public PageResult<SessionCardRecordDTO> getSessionCardRecordPage(SessionCardRecordPageReqVO pageReqVO) {
        return sessionCardRecordMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<SessionCardRecordDO> getSessionCardRecordList(Long sessionId) {
        return sessionCardRecordMapper.selectList(SessionCardRecordDO::getSessionId, sessionId);
    }

}