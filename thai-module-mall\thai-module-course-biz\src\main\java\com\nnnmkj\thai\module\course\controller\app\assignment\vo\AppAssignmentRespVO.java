package com.nnnmkj.thai.module.course.controller.app.assignment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 App - 课程作业 Response VO")
@Data
public class AppAssignmentRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "23410")
    private Long id;

    @Schema(description = "作业名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String name;

    @Schema(description = "满分", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Double fullMarks;

    @Schema(description = "题目数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer questionCount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}