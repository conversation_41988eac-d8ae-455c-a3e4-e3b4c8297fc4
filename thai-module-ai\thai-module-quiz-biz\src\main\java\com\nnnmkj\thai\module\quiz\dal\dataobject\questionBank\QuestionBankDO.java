package com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 题目 DO
 *
 * <AUTHOR>
 */
@TableName("quiz_question_bank")
@KeySequence("quiz_question_bank_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionBankDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * AI出题ID
     */
    private Long aiQuestionId;
    /**
     * 题目类型
     */
    private Integer questionType;
    /**
     * 创建方式
     */
    private Integer createMethod;
    /**
     * 是否公开
     */
    private Boolean publicStatus;
    /**
     * 题干
     */
    private String questionStem;
    /**
     * 参考答案
     */
    private String answer;
    /**
     * 题目出处
     */
    private String source;
    /**
     * 试题解析
     */
    private String analysis;
    /**
     * 难易度
     */
    private Integer difficulty;
    /**
     * 未通过原因
     */
    private String rejectReason;
    /**
     * 审核状态
     */
    private Integer auditStatus;

}