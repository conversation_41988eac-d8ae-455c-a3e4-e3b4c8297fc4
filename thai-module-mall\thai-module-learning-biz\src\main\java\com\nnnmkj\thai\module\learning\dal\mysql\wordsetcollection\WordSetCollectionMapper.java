package com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollection;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordsetcollection.vo.AppWordSetCollectionPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习集收藏 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordSetCollectionMapper extends BaseMapperX<WordSetCollectionDO> {

    default PageResult<WordSetCollectionDO> selectPage(WordSetCollectionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<WordSetCollectionDO>()
                .eqIfPresent(WordSetCollectionDO::getSetId, reqVO.getSetId())
                .likeIfPresent(WordSetCollectionDO::getCreator, creatorQuery)
                .betweenIfPresent(WordSetCollectionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordSetCollectionDO::getId));
    }

    default PageResult<WordSetCollectionDO> selectPage(AppWordSetCollectionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<WordSetCollectionDO>()
                .eqIfPresent(WordSetCollectionDO::getSetId, reqVO.getSetId())
                .likeIfPresent(WordSetCollectionDO::getCreator, creatorQuery)
                .betweenIfPresent(WordSetCollectionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordSetCollectionDO::getId));
    }

}