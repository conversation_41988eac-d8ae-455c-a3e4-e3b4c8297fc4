package com.nnnmkj.thai.module.quiz.service.aiQuestionGenerateTask;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskSaveReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskRecordDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * AI出题生成任务 Service 接口
 *
 * <AUTHOR>
 */
public interface AiQuestionGenerateTaskService {

    /**
     * 创建AI出题生成任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAiQuestionGenerateTask(@Valid AiQuestionGenerateTaskSaveReqVO createReqVO);

    /**
     * 更新AI出题生成任务
     *
     * @param updateReqVO 更新信息
     */
    void updateAiQuestionGenerateTask(@Valid AiQuestionGenerateTaskSaveReqVO updateReqVO);

    /**
     * 删除AI出题生成任务
     *
     * @param id 编号
     */
    void deleteAiQuestionGenerateTask(Long id);

    /**
     * 获得AI出题生成任务
     *
     * @param id 编号
     * @return AI出题生成任务
     */
    AiQuestionGenerateTaskDO getAiQuestionGenerateTask(Long id);

    /**
     * 获得AI出题生成任务分页
     *
     * @param pageReqVO 分页查询
     * @return AI出题生成任务分页
     */
    PageResult<AiQuestionGenerateTaskDTO> getAiQuestionGenerateTaskPage(AiQuestionGenerateTaskPageReqVO pageReqVO);

    // ==================== 子表（AI出题生成任务记录） ====================

    /**
     * 获得AI出题生成任务记录列表
     *
     * @param taskId 任务ID
     * @return AI出题生成任务记录列表
     */
    List<AiQuestionGenerateTaskRecordDO> getAiQuestionGenerateTaskRecordListByTaskId(Long taskId);

}