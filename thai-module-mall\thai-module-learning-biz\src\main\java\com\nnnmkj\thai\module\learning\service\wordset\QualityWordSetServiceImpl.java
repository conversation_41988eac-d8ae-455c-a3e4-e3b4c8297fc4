package com.nnnmkj.thai.module.learning.service.wordset;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.json.JsonUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetRespVO;
import com.nnnmkj.thai.module.learning.convert.wordset.WordSetConvert;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapperX;
import com.nnnmkj.thai.module.learning.enums.IsStoreStatusEnum;
import com.nnnmkj.thai.module.learning.service.wordsetcollection.AppWordSetCollectionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 优质学习集 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class QualityWordSetServiceImpl implements QualityWordSetService {

    // 缓存相关常量
    private static final String CACHE_KEY_QUALITY_WORD_SETS = "learning:word_set:quality";
    private static final String CACHE_KEY_USER_VIEW_PREFIX = "learning:word_set:user_view:";
    private static final String REDIS_KEY_VIEW_COUNT = "learning:word_set:view_count";
    private static final String REDIS_KEY_SYNC_LOCK = "learning:word_set:view_count_sync_lock";
    private static final String REDIS_KEY_LAST_SYNC_TIME = "learning:word_set:view_count_last_sync_time";

    // 时间相关常量
    private static final long CACHE_EXPIRE_TIME = 15 * 60; // 15分钟缓存过期时间
    private static final long VIEW_LOCK_TIME = 10 * 60; // 10分钟内重复点击不计数
    private static final long SYNC_INTERVAL = 10 * 60 * 1000; // 10分钟同步间隔
    private static final long SYNC_LOCK_TIME = 30; // 同步锁过期时间（30秒）

    @Resource
    private WordSetMapperX wordSetMapperX;

    @Resource
    private WordSetMapper wordSetMapper;

    @Resource
    private AppWordSetCollectionService wordSetCollectionService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public PageResult<AppWordSetRespVO> getQualityWordSets(AppWordSetPageReqVO pageReqVO) {
        // 获取当前登录用户ID
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 构建缓存key，包含排序参数和标题
        String cacheKey = String.format("%s:%d:%d:%s", CACHE_KEY_QUALITY_WORD_SETS,
                pageReqVO.getPageNo(), pageReqVO.getPageSize(), 
                pageReqVO.getTitle() != null ? pageReqVO.getTitle() : "");
        String cachedData = stringRedisTemplate.opsForValue().get(cacheKey);

        // 如果缓存中有数据，直接返回
        try {
            // 使用 JsonUtils 将 JSON 字符串反序列化为 PageResult<AppWordSetRespVO>
            PageResult<AppWordSetRespVO> cachedResult = JsonUtils.parseObject(cachedData,
                    new TypeReference<>() {});
            log.info("[优质学习集] 从缓存中获取数据，页码：{}，每页大小：{}，总数：{}",
                pageReqVO.getPageNo(), pageReqVO.getPageSize(), cachedResult.getTotal());

            // 如果用户已登录，需要处理收藏状态
            if (userId != null && !cachedResult.getList().isEmpty()) {
                updateCollectionStatus(cachedResult.getList(), userId, pageReqVO.getUserType());
            }

            return cachedResult;
        } catch (Exception e) {
            // 反序列化失败，删除缓存并重新查询
            log.error("[优质学习集] 缓存数据反序列化失败", e);
            stringRedisTemplate.delete(cacheKey);
        }

        // 缓存中没有数据，从数据库查询
        PageResult<WordSetDTO> pageResult = wordSetMapperX.selectQualityWordSets(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>();
        }

        // 处理是否收藏返回值
        Set<Long> setIds = pageResult.getList().stream()
                .map(WordSetDTO::getId)
                .collect(Collectors.toSet());

        // 获取用户收藏的学习集
        Set<Long> storeSetIds = Collections.emptySet();
        if (userId != null && !setIds.isEmpty()) {
            List<WordSetCollectionDO> wordSetCollectionList = wordSetCollectionService.getWordSetCollectionList(setIds, userId, pageReqVO.getUserType());
            storeSetIds = wordSetCollectionList.stream()
                    .map(WordSetCollectionDO::getSetId)
                    .collect(Collectors.toSet());
        }

        // 转换为响应对象
        PageResult<AppWordSetRespVO> result = WordSetConvert.INSTANCE.convertAppPage(pageResult, storeSetIds);

        // 将结果缓存到Redis，设置过期时间
        try {
            // 使用 JsonUtils 将 PageResult<AppWordSetRespVO> 序列化为 JSON 字符串
            String jsonData = JsonUtils.toJsonString(result);
            stringRedisTemplate.opsForValue().set(cacheKey, jsonData, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
            log.info("[优质学习集] 将数据缓存到Redis，页码：{}，每页大小：{}，总数：{}",
                pageReqVO.getPageNo(), pageReqVO.getPageSize(), result.getTotal());
        } catch (Exception e) {
            log.error("[优质学习集] 缓存数据序列化失败", e);
        }

        return result;
    }

    /**
     * 更新学习集列表的收藏状态
     *
     * @param wordSetList 学习集列表
     * @param userId 用户ID
     */
    private void updateCollectionStatus(List<AppWordSetRespVO> wordSetList, Long userId, Integer userType) {
        if (CollUtil.isEmpty(wordSetList) || userId == null) {
            return;
        }

        // 获取学习集ID列表
        Set<Long> setIds = wordSetList.stream()
                .map(AppWordSetRespVO::getId)
                .collect(Collectors.toSet());

        // 查询用户收藏的学习集
        List<WordSetCollectionDO> collections = wordSetCollectionService.getWordSetCollectionList(setIds, userId, userType);
        Set<Long> collectedSetIds = collections.stream()
                .map(WordSetCollectionDO::getSetId)
                .collect(Collectors.toSet());

        // 更新收藏状态
        for (AppWordSetRespVO wordSet : wordSetList) {
            boolean isCollected = collectedSetIds.contains(wordSet.getId());
            wordSet.setIsStore(isCollected ? IsStoreStatusEnum.YES.getType() : IsStoreStatusEnum.NO.getType());
        }
    }

    @Override
    public void incrementViewCount(Long setId, Long userId) {
        if (setId == null || userId == null) {
            log.warn("[增加点击量] 参数无效，setId={}, userId={}", setId, userId);
            return;
        }

        // 防刷机制：检查用户是否在短时间内已经点击过该学习集
        String viewLockKey = CACHE_KEY_USER_VIEW_PREFIX + userId + ":" + setId;
        Boolean isFirstSet = stringRedisTemplate.opsForValue().setIfAbsent(viewLockKey, "1", VIEW_LOCK_TIME, TimeUnit.SECONDS);

        // 如果已经点击过，则不增加点击量
        if (Boolean.FALSE.equals(isFirstSet)) {
            log.info("[增加点击量] 用户{}10分钟内重复点击学习集{}，不计数", userId, setId);
            return;
        }

        // 在Redis中累积点击量，使用Hash结构存储学习集的点击量
        stringRedisTemplate.opsForHash().increment(REDIS_KEY_VIEW_COUNT, setId.toString(), 1);
        log.info("[增加点击量] 学习集{}点击量在缓存中增加1", setId);

        // 检查是否需要将缓存中的点击量同步到数据库
        // 使用分布式锁确保只有一个实例执行同步操作
        Boolean gotLock = stringRedisTemplate.opsForValue().setIfAbsent(REDIS_KEY_SYNC_LOCK, "1", SYNC_LOCK_TIME, TimeUnit.SECONDS);

        if (Boolean.TRUE.equals(gotLock)) {
            try {
                // 获取当前时间戳
                String lastSyncTimeStr = stringRedisTemplate.opsForValue().get(REDIS_KEY_LAST_SYNC_TIME);
                long currentTime = System.currentTimeMillis();
                long lastSyncTime = Long.parseLong(lastSyncTimeStr);

                // 如果距离上次同步已经过了10分钟，则执行同步
                if (currentTime - lastSyncTime >= SYNC_INTERVAL) {
                    syncViewCountsToDatabase();

                    // 更新最后同步时间
                    stringRedisTemplate.opsForValue().set(REDIS_KEY_LAST_SYNC_TIME, String.valueOf(currentTime));
                }
            } finally {
                // 释放锁
                stringRedisTemplate.delete(REDIS_KEY_SYNC_LOCK);
            }
        }
    }

    /**
     * 将缓存中的点击量同步到数据库
     */
    public void syncViewCountsToDatabase() {
        // 获取所有学习集的缓存点击量
        Map<Object, Object> allViewCounts = stringRedisTemplate.opsForHash().entries(REDIS_KEY_VIEW_COUNT);

        if (allViewCounts.isEmpty()) {
            log.info("[同步点击量] 缓存中没有需要同步的点击量数据");
            return;
        }

        log.info("[同步点击量] 开始同步缓存点击量到数据库，共{}个学习集", allViewCounts.size());

        // 准备批量更新的学习集列表
        List<WordSetDO> wordSetsToUpdate = new ArrayList<>();

        // 首先获取所有需要更新的学习集ID
        Set<Long> setIds = allViewCounts.keySet().stream()
                .map(key -> Long.valueOf(key.toString()))
                .collect(Collectors.toSet());

        // 批量查询这些学习集
        if (!setIds.isEmpty()) {
            List<WordSetDO> wordSets = wordSetMapper.selectList(
                    new LambdaQueryWrapperX<WordSetDO>()
                            .in(WordSetDO::getId, setIds)
            );
            Map<Long, WordSetDO> wordSetMap = wordSets.stream()
                    .collect(Collectors.toMap(WordSetDO::getId, wordSet -> wordSet));

            // 遍历所有学习集的点击量，更新到数据库
            for (Map.Entry<Object, Object> entry : allViewCounts.entrySet()) {
                try {
                    Long setIdToUpdate = Long.valueOf(entry.getKey().toString());
                    int increment = Integer.parseInt(entry.getValue().toString());

                    // 从映射中获取学习集
                    WordSetDO wordSet = wordSetMap.get(setIdToUpdate);
                    if (wordSet != null) {
                        Integer viewCount = wordSet.getViewCount();
                        if (viewCount == null) {
                            viewCount = increment;
                        } else {
                            viewCount += increment;
                        }
                        wordSet.setViewCount(viewCount);
                        wordSetsToUpdate.add(wordSet);
                    } else {
                        log.warn("[同步点击量] 学习集{}不存在", setIdToUpdate);
                    }
                } catch (Exception e) {
                    log.error("[同步点击量] 处理学习集{}点击量失败", entry.getKey(), e);
                }
            }

            // 批量更新数据库
            if (!wordSetsToUpdate.isEmpty()) {
                try {
                    // 使用批量更新提高性能
                    batchUpdateWordSets(wordSetsToUpdate);
                    log.info("[同步点击量] 批量更新{}个学习集点击量到数据库成功", wordSetsToUpdate.size());
                } catch (Exception e) {
                    log.error("[同步点击量] 批量更新学习集点击量失败", e);
                    throw e; // 重新抛出异常，触发事务回滚
                }
            }
        }

        // 清空缓存中的点击量计数
        stringRedisTemplate.delete(REDIS_KEY_VIEW_COUNT);

        // 清除优质学习集缓存，以便下次查询时重新计算
        Set<String> keys = stringRedisTemplate.keys(CACHE_KEY_QUALITY_WORD_SETS + ":*");
        if (CollUtil.isNotEmpty(keys)) {
            stringRedisTemplate.delete(keys);
            log.info("[同步点击量] 清除优质学习集缓存{}个", keys.size());
        }
    }
    /**
     * 批量更新学习集
     *
     * @param wordSets 需要更新的学习集列表
     */
    public void batchUpdateWordSets(List<WordSetDO> wordSets) {
        if (CollUtil.isEmpty(wordSets)) {
            return;
        }

        // 如果数据量小，使用单次批量更新
        if (wordSets.size() <= 100) {
            wordSetMapper.updateBatch(wordSets);
            return;
        }

        // 数据量大时，分批次批量更新，每批100条
        int batchSize = 100;
        int totalSize = wordSets.size();
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<WordSetDO> batch = wordSets.subList(i, endIndex);
            wordSetMapper.updateBatch(batch);
            log.info("[批量更新] 完成第{}批，共{}条", (i / batchSize) + 1, batch.size());
        }
    }
}
