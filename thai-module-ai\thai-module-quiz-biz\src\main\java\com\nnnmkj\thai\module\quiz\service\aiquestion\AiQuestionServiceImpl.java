package com.nnnmkj.thai.module.quiz.service.aiquestion;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionSaveReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDTO;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiquestion.AiQuestionMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiquestion.AiQuestionMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.quiz.enums.ErrorCodeConstants.AI_QUESTION_NOT_EXISTS;

/**
 * AI出题 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AiQuestionServiceImpl implements AiQuestionService {

    @Resource
    private AiQuestionMapper aiQuestionMapper;
    @Resource
    private AiQuestionMapperX aiQuestionMapperX;

    @Override
    public Long createAiQuestion(AiQuestionSaveReqVO createReqVO) {
        // 插入
        AiQuestionDO aiQuestion = BeanUtils.toBean(createReqVO, AiQuestionDO.class);
        aiQuestionMapper.insert(aiQuestion);
        // 返回
        return aiQuestion.getId();
    }

    @Override
    public void updateAiQuestion(AiQuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateAiQuestionExists(updateReqVO.getId());
        // 更新
        AiQuestionDO updateObj = BeanUtils.toBean(updateReqVO, AiQuestionDO.class);
        aiQuestionMapper.updateById(updateObj);
    }

    @Override
    public void deleteAiQuestion(Long id) {
        // 校验存在
        validateAiQuestionExists(id);
        // 删除
        aiQuestionMapper.deleteById(id);
    }

    private void validateAiQuestionExists(Long id) {
        if (aiQuestionMapper.selectById(id) == null) {
            throw exception(AI_QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public AiQuestionDO getAiQuestion(Long id) {
        return aiQuestionMapper.selectById(id);
    }

    @Override
    public PageResult<AiQuestionDTO> getAiQuestionPage(AiQuestionPageReqVO pageReqVO) {
        return aiQuestionMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<AiQuestionDO> getAiQuestionList(AiQuestionPageReqVO reqVO) {
        return aiQuestionMapper.selectList(reqVO);
    }
}