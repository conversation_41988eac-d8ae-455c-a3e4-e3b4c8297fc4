package com.nnnmkj.thai.module.course.dal.mysql.lesson;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.lesson.vo.AppLessonPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 课程 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonMapper extends BaseMapperX<LessonDO> {

    default PageResult<LessonDO> selectPage(LessonPageReqVO reqVO) {
        // String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), WebFrameworkUtils.getLoginUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonDO>()
                .likeIfPresent(LessonDO::getTitle, reqVO.getTitle())
                .likeIfPresent(LessonDO::getDescription, reqVO.getDescription())
                .likeIfPresent(LessonDO::getCoverImage, reqVO.getCoverImage())
                // .likeIfPresent(LessonDO::getCreator, creatorQuery)
                .betweenIfPresent(LessonDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonDO::getId));
    }

    default List<LessonDO> selectList(LessonPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<LessonDO>()
                .likeIfPresent(LessonDO::getTitle, reqVO.getTitle())
                .likeIfPresent(LessonDO::getDescription, reqVO.getDescription())
                .likeIfPresent(LessonDO::getCoverImage, reqVO.getCoverImage())
                // .eqIfPresent(LessonDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(LessonDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonDO::getId));
    }

    default PageResult<LessonDO> selectPage(AppLessonPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LessonDO>()
                .likeIfPresent(LessonDO::getTitle, reqVO.getTitle())
                .likeIfPresent(LessonDO::getDescription, reqVO.getDescription())
                .likeIfPresent(LessonDO::getCoverImage, reqVO.getCoverImage())
                .eqIfPresent(LessonDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(LessonDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LessonDO::getId));
    }

}