<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.nnnmkj</groupId>
        <artifactId>thai-module-ai</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>thai-module-phonetic-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        phonetic 模块，主要实现发音训练功能
    </description>

    <dependencies>
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-module-phonetic-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-module-ai-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 文件服务相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-ai</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Job 相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 语音识别相关 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

        <!-- 微软 发音评估 服务 -->
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>jakarta.json</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.java-diff-utils</groupId>
            <artifactId>java-diff-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.microsoft.cognitiveservices.speech</groupId>
            <artifactId>client-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nnnmkj</groupId>
            <artifactId>thai-spring-boot-starter-excel</artifactId>
        </dependency>
    </dependencies>

</project>