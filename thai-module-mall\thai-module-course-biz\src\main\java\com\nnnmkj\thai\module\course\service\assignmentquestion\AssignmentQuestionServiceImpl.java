package com.nnnmkj.thai.module.course.service.assignmentquestion;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionCreateFromBankReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionCreateFromBankReqVO.QuestionScoreItem;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionOptionDO;
import com.nnnmkj.thai.module.course.dal.mysql.assignmentquestion.AssignmentQuestionMapper;
import com.nnnmkj.thai.module.course.dal.mysql.assignmentquestion.AssignmentQuestionOptionMapper;
import com.nnnmkj.thai.module.quiz.api.questionbank.QuestionBankApi;
import com.nnnmkj.thai.module.quiz.api.questionbank.dto.QuestionBankOptionRespDTO;
import com.nnnmkj.thai.module.quiz.api.questionbank.dto.QuestionBankRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.ASSIGNMENT_QUESTION_NOT_EXISTS;

/**
 * 课程作业题目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssignmentQuestionServiceImpl implements AssignmentQuestionService {

    @Resource
    private AssignmentQuestionMapper assignmentQuestionMapper;
    @Resource
    private AssignmentQuestionOptionMapper assignmentQuestionOptionMapper;
    @Resource
    private QuestionBankApi questionBankApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createQuestionFromBank(AssignmentQuestionCreateFromBankReqVO createReqVO) {
        // 获取题目ID列表用于查询题库
        List<Long> questionIds = CollectionUtils.convertList(createReqVO.getQuestionItems(), QuestionScoreItem::getQuestionId);

        // 查询题库信息和选项
        List<QuestionBankRespDTO> questionBankList = questionBankApi.getQuestionBankList(questionIds);
        List<QuestionBankOptionRespDTO> optionList = questionBankApi.getQuestionBankOptionListByQuestionIds(questionIds);
        Map<Long, List<QuestionBankOptionRespDTO>> optionMap = CollectionUtils.convertMultiMap(optionList, QuestionBankOptionRespDTO::getQuestionId);

        // 构建 saveReqVOList 和 assignmentQuestions 列表
        List<AssignmentQuestionSaveReqVO> saveReqVOList = new ArrayList<>();
        List<AssignmentQuestionDO> assignmentQuestions = new ArrayList<>();

        // 题目分数映射
        Map<Long, Double> questionScoreMap = CollectionUtils.convertMap(createReqVO.getQuestionItems(), QuestionScoreItem::getQuestionId, QuestionScoreItem::getScore);

        for (QuestionBankRespDTO questionBank : questionBankList) {
            AssignmentQuestionSaveReqVO saveReqVO = new AssignmentQuestionSaveReqVO();
            saveReqVO.setCourseId(createReqVO.getCourseId());
            saveReqVO.setAssignmentId(createReqVO.getAssignmentId());
            saveReqVO.setQuestionType(questionBank.getQuestionType());
            saveReqVO.setQuestionStem(questionBank.getQuestionStem());
            saveReqVO.setAnswer(questionBank.getAnswer());
            saveReqVO.setSource(questionBank.getSource());
            saveReqVO.setAnalysis(questionBank.getAnalysis());
            saveReqVO.setDifficulty(questionBank.getDifficulty());

            // 设置题目分数
            saveReqVO.setScore(questionScoreMap.get(questionBank.getId()));

            // 处理题目选项
            List<QuestionBankOptionRespDTO> optionDTOs = optionMap.get(questionBank.getId());
            List<AssignmentQuestionOptionDO> optionDOs = BeanUtils.toBean(optionDTOs, AssignmentQuestionOptionDO.class);
            optionDOs.forEach(option -> {
                option.setId(null);
                option.setQuestionId(null);
            });
            saveReqVO.setOptionList(optionDOs);
            saveReqVOList.add(saveReqVO);

            // 转换为 DO 并添加到批量插入列表
            AssignmentQuestionDO assignmentQuestion = BeanUtils.toBean(saveReqVO, AssignmentQuestionDO.class);
            assignmentQuestions.add(assignmentQuestion);
        }

        // 批量插入主表数据
        assignmentQuestionMapper.insertBatch(assignmentQuestions);

        // 回填主键 ID，并插入选项数据（按顺序一一对应）
        for (int i = 0; i < saveReqVOList.size(); i++) {
            AssignmentQuestionSaveReqVO saveReqVO = saveReqVOList.get(i);
            AssignmentQuestionDO insertedDO = assignmentQuestions.get(i);
            Long questionId = insertedDO.getId();

            // 设置主键 ID 到 saveReqVO
            saveReqVO.setId(questionId);

            // 插入题目选项数据
            if (CollectionUtil.isNotEmpty(saveReqVO.getOptionList())) {
                saveReqVO.getOptionList().forEach(option -> option.setQuestionId(questionId));
                assignmentQuestionOptionMapper.insertBatch(saveReqVO.getOptionList());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAssignmentQuestion(AssignmentQuestionSaveReqVO createReqVO) {
        // 插入
        AssignmentQuestionDO assignmentQuestion = BeanUtils.toBean(createReqVO, AssignmentQuestionDO.class);
        assignmentQuestionMapper.insert(assignmentQuestion);

        // 插入子表
        createAssignmentQuestionOptionList(assignmentQuestion.getId(), createReqVO.getOptionList());
        // 返回
        return assignmentQuestion.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAssignmentQuestion(AssignmentQuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateAssignmentQuestionExists(updateReqVO.getId());
        // 更新
        AssignmentQuestionDO updateObj = BeanUtils.toBean(updateReqVO, AssignmentQuestionDO.class);
        assignmentQuestionMapper.updateById(updateObj);

        // 更新子表
        updateAssignmentQuestionOptionList(updateReqVO.getId(), updateReqVO.getOptionList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAssignmentQuestion(Long id) {
        // 校验存在
        validateAssignmentQuestionExists(id);
        // 删除
        assignmentQuestionMapper.deleteById(id);
        // 删除子表
        deleteAssignmentQuestionOptionByQuestionId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAssignmentQuestionByAssignmentId(Long id) {
        List<AssignmentQuestionDO> questions = assignmentQuestionMapper.selectList(AssignmentQuestionDO::getAssignmentId, id);
        if (CollUtil.isEmpty(questions)) {
            return;
        }
        Set<Long> questionIds = CollectionUtils.convertSet(questions, AssignmentQuestionDO::getId);
        assignmentQuestionMapper.deleteByIds(questionIds);
        assignmentQuestionOptionMapper.deleteByQuestionIds(questionIds);
    }

    private void validateAssignmentQuestionExists(Long id) {
        if (assignmentQuestionMapper.selectById(id) == null) {
            throw exception(ASSIGNMENT_QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public AssignmentQuestionDO getAssignmentQuestion(Long id) {
        return assignmentQuestionMapper.selectById(id);
    }

    @Override
    public List<AssignmentQuestionDO> getAssignmentQuestionListByAssignmentId(Long id) {
        return getAssignmentQuestionListByAssignmentIds(Collections.singletonList(id));
    }

    @Override
    public List<AssignmentQuestionDO> getAssignmentQuestionListByAssignmentIds(List<Long> ids) {
        return assignmentQuestionMapper.selectList(AssignmentQuestionDO::getAssignmentId, ids);
    }

    @Override
    public PageResult<AssignmentQuestionDO> getAssignmentQuestionPage(AssignmentQuestionPageReqVO pageReqVO) {
        return assignmentQuestionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AssignmentQuestionDO> getAssignmentQuestionList(List<Long> questionIds) {
        if (questionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return assignmentQuestionMapper.selectByIds(questionIds);
    }

    // ==================== 子表（课程作业题目选项） ====================

    @Override
    public List<AssignmentQuestionOptionDO> getAssignmentQuestionOptionListByQuestionId(Long questionId) {
        return assignmentQuestionOptionMapper.selectListByQuestionId(questionId);
    }

    @Override
    public Map<Long, List<AssignmentQuestionOptionDO>> getAssignmentQuestionOptionMapByQuestionIds(List<Long> questionIds) {
        if (questionIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<AssignmentQuestionOptionDO> optionDOS = assignmentQuestionOptionMapper.selectListByQuestionIds(questionIds);
        return optionDOS.stream().collect(Collectors.groupingBy(AssignmentQuestionOptionDO::getQuestionId));
    }

    private void createAssignmentQuestionOptionList(Long questionId, List<AssignmentQuestionOptionDO> list) {
        list.forEach(o -> o.setQuestionId(questionId));
        assignmentQuestionOptionMapper.insertBatch(list);
    }

    private void updateAssignmentQuestionOptionList(Long questionId, List<AssignmentQuestionOptionDO> list) {
        deleteAssignmentQuestionOptionByQuestionId(questionId);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createAssignmentQuestionOptionList(questionId, list);
    }

    private void deleteAssignmentQuestionOptionByQuestionId(Long questionId) {
        assignmentQuestionOptionMapper.deleteByQuestionId(questionId);
    }

    private void deleteAssignmentQuestionOptionByQuestionIds(List<Long> questionId) {
        assignmentQuestionOptionMapper.deleteByQuestionIds(questionId);
    }

}