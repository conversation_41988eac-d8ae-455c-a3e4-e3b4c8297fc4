package com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDTO;
import com.nnnmkj.thai.module.learning.service.sessionstudyrecord.SessionStudyRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 学习记录")
@RestController
@RequestMapping("/learning/session-study-record")
@Validated
public class SessionStudyRecordController {

    @Resource
    private SessionStudyRecordService sessionStudyRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建学习记录")
    @PreAuthorize("@ss.hasPermission('learning:session-study-record:create')")
    public CommonResult<Long> createSessionStudyRecord(@Valid @RequestBody SessionStudyRecordSaveReqVO createReqVO) {
        return success(sessionStudyRecordService.createSessionStudyRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新学习记录")
    @PreAuthorize("@ss.hasPermission('learning:session-study-record:update')")
    public CommonResult<Boolean> updateSessionStudyRecord(@Valid @RequestBody SessionStudyRecordSaveReqVO updateReqVO) {
        sessionStudyRecordService.updateSessionStudyRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除学习记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:session-study-record:delete')")
    public CommonResult<Boolean> deleteSessionStudyRecord(@RequestParam("id") Long id) {
        sessionStudyRecordService.deleteSessionStudyRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得学习记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:session-study-record:query')")
    public CommonResult<SessionStudyRecordRespVO> getSessionStudyRecord(@RequestParam("id") Long id) {
        SessionStudyRecordDO sessionStudyRecord = sessionStudyRecordService.getSessionStudyRecord(id);
        return success(BeanUtils.toBean(sessionStudyRecord, SessionStudyRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得学习记录分页")
    @PreAuthorize("@ss.hasPermission('learning:session-study-record:query')")
    public CommonResult<PageResult<SessionStudyRecordRespVO>> getSessionStudyRecordPage(@Valid SessionStudyRecordPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<SessionStudyRecordDTO> pageResult = sessionStudyRecordService.getSessionStudyRecordPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, SessionStudyRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出学习记录 Excel")
    @PreAuthorize("@ss.hasPermission('learning:session-study-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSessionStudyRecordExcel(@Valid SessionStudyRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        List<SessionStudyRecordDTO> list = sessionStudyRecordService.getSessionStudyRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "学习记录.xls", "数据", SessionStudyRecordRespVO.class,
                        BeanUtils.toBean(list, SessionStudyRecordRespVO.class));
    }

}