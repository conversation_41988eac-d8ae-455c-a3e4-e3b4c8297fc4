package com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollection;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习集收藏 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordSetCollectionMapperX extends BaseMapperX<WordSetCollectionDTO> {

    default PageResult<WordSetCollectionDTO> selectPage(WordSetCollectionPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<WordSetCollectionDTO> wrapper = new MPJLambdaWrapperX<WordSetCollectionDTO>()
                .selectAll(WordSetCollectionDO.class)
                .selectAs("t1.nickname", WordSetCollectionDTO::getNickname)
                .selectAs(WordSetDO::getTitle, WordSetCollectionDTO::getSetTitle)
                .eqIfPresent(WordSetCollectionDO::getSetId, reqVO.getSetId())
                .likeIfPresent(WordSetCollectionDO::getCreator, creatorQuery)
                .betweenIfPresent(WordSetCollectionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordSetCollectionDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, WordSetCollectionDO::getSetId);
        return selectPage(reqVO, wrapper);
    }
}