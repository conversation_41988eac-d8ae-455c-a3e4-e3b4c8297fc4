package com.nnnmkj.thai.module.learning.service.sessionstudyrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDTO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 学习记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SessionStudyRecordService {

    /**
     * 创建学习记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSessionStudyRecord(@Valid SessionStudyRecordSaveReqVO createReqVO);

    /**
     * 更新学习记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSessionStudyRecord(@Valid SessionStudyRecordSaveReqVO updateReqVO);

    /**
     * 删除学习记录
     *
     * @param id 编号
     */
    void deleteSessionStudyRecord(Long id);

    /**
     * 获得学习记录
     *
     * @param id 编号
     * @return 学习记录
     */
    SessionStudyRecordDO getSessionStudyRecord(Long id);

    /**
     * 获得学习记录分页
     *
     * @param pageReqVO 分页查询
     * @return 学习记录分页
     */
    PageResult<SessionStudyRecordDTO> getSessionStudyRecordPage(SessionStudyRecordPageReqVO pageReqVO);

    /**
     * 获得学习记录列表
     * @param sessionId 学习会话编号
     * @return 学习记录列表
     */
    List<SessionStudyRecordDO> getSessionStudyRecordList(Long sessionId);

}