package com.nnnmkj.thai.module.learning.dal.mysql.wordset;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 学习集 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordSetMapper extends BaseMapperX<WordSetDO> {

    default LambdaQueryWrapperX<WordSetDO> buildCommonCondition(WordSetPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return new LambdaQueryWrapperX<WordSetDO>()
                .likeIfPresent(WordSetDO::getTitle, reqVO.getTitle())
                .eqIfPresent(WordSetDO::getVisibility, reqVO.getVisibility())
                .likeIfPresent(WordSetDO::getCreator, creatorQuery)
                .betweenIfPresent(WordSetDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordSetDO::getId);
    }

    default PageResult<WordSetDO> selectPage(WordSetPageReqVO reqVO) {
        return selectPage(reqVO, buildCommonCondition(reqVO));
    }

    default List<WordSetDO> selectList(WordSetPageReqVO reqVO) {
        return selectList(buildCommonCondition(reqVO));
    }

    default PageResult<WordSetDO> selectPage(AppWordSetPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<WordSetDO>()
                .likeIfPresent(WordSetDO::getTitle, reqVO.getTitle())
                .eqIfPresent(WordSetDO::getVisibility, reqVO.getVisibility())
                .likeIfPresent(WordSetDO::getCreator, creatorQuery)
                .betweenIfPresent(WordSetDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordSetDO::getId));
    }
}