package com.nnnmkj.thai.module.learning.service.wordsetcollection;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.learning.controller.app.wordsetcollection.vo.AppWordSetCollectionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollection.WordSetCollectionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollectionstatistic.WordSetCollectionStatisticMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.WORD_SET_COLLECTION_NOT_EXISTS;

/**
 * 学习集收藏 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppWordSetCollectionServiceImpl implements AppWordSetCollectionService {

    @Resource
    private WordSetCollectionMapper wordSetCollectionMapper;

    @Resource
    private WordSetCollectionStatisticMapper wordSetCollectionStatisticMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createWordSetCollection(AppWordSetCollectionSaveReqVO createReqVO) {
        // 插入
        WordSetCollectionDO wordSetCollection = BeanUtils.toBean(createReqVO, WordSetCollectionDO.class);
        wordSetCollectionMapper.insert(wordSetCollection);

        Long setId = wordSetCollection.getSetId();
        // 更新学习集收藏统计
        wordSetCollectionStatisticMapper.atomicUpdateCount(setId, 1);
        // 返回
        return wordSetCollection.getId();
    }

    @Override
    public List<WordSetCollectionDO> getWordSetCollectionList(Collection<Long> setIds, Long userId, Integer userType) {
        if (CollUtil.isEmpty(setIds)) {
            return Collections.emptyList();
        }
        String creatorQuery = CommonUtils.getCreatorQuery(userId, userType);
        return wordSetCollectionMapper.selectList(new LambdaQueryWrapperX<WordSetCollectionDO>()
                .in(WordSetCollectionDO::getSetId, setIds)
                .likeIfPresent(WordSetCollectionDO::getCreator, creatorQuery));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWordSetCollection(Long setId, Long userId) {
        // 校验存在
        validateWordSetCollectionExists(setId, userId);
        // 删除
        wordSetCollectionMapper.delete(new LambdaQueryWrapperX<WordSetCollectionDO>()
                .eq(WordSetCollectionDO::getSetId, setId)
                .eq(WordSetCollectionDO::getUserId, userId));
        // 更新学习集收藏统计
        wordSetCollectionStatisticMapper.atomicUpdateCount(setId, -1);
    }

    private void validateWordSetCollectionExists(Long setId, Long userId) {
        String creatorQuery = CommonUtils.getCreatorQuery(userId, WebFrameworkUtils.getLoginUserType());
        LambdaQueryWrapperX<WordSetCollectionDO> wrapperX = new LambdaQueryWrapperX<WordSetCollectionDO>()
                .eq(WordSetCollectionDO::getSetId, setId)
                .likeIfPresent(WordSetCollectionDO::getCreator, creatorQuery);
        List<WordSetCollectionDO> wordSetCollectionDOS = wordSetCollectionMapper.selectList(wrapperX);
        if (CollUtil.isEmpty(wordSetCollectionDOS)) {
            throw exception(WORD_SET_COLLECTION_NOT_EXISTS);
        }
    }
}