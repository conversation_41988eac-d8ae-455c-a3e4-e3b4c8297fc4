package com.nnnmkj.thai.module.learning.service.sessionstudyrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionstudyrecord.vo.SessionStudyRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord.SessionStudyRecordDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.sessionstudyrecord.SessionStudyRecordMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.sessionstudyrecord.SessionStudyRecordMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.SESSION_STUDY_RECORD_NOT_EXISTS;

/**
 * 学习记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SessionStudyRecordServiceImpl implements SessionStudyRecordService {

    @Resource
    private SessionStudyRecordMapper sessionStudyRecordMapper;

    @Resource
    private SessionStudyRecordMapperX sessionStudyRecordMapperX;

    @Override
    public Long createSessionStudyRecord(SessionStudyRecordSaveReqVO createReqVO) {
        // 插入
        SessionStudyRecordDO sessionStudyRecord = BeanUtils.toBean(createReqVO, SessionStudyRecordDO.class);
        sessionStudyRecordMapper.insert(sessionStudyRecord);
        // 返回
        return sessionStudyRecord.getId();
    }

    @Override
    public void updateSessionStudyRecord(SessionStudyRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateSessionStudyRecordExists(updateReqVO.getId());
        // 更新
        SessionStudyRecordDO updateObj = BeanUtils.toBean(updateReqVO, SessionStudyRecordDO.class);
        sessionStudyRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteSessionStudyRecord(Long id) {
        // 校验存在
        validateSessionStudyRecordExists(id);
        // 删除
        sessionStudyRecordMapper.deleteById(id);
    }

    private void validateSessionStudyRecordExists(Long id) {
        if (sessionStudyRecordMapper.selectById(id) == null) {
            throw exception(SESSION_STUDY_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public SessionStudyRecordDO getSessionStudyRecord(Long id) {
        return sessionStudyRecordMapper.selectById(id);
    }

    @Override
    public PageResult<SessionStudyRecordDTO> getSessionStudyRecordPage(SessionStudyRecordPageReqVO pageReqVO) {
        return sessionStudyRecordMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<SessionStudyRecordDO> getSessionStudyRecordList(Long sessionId) {
        return sessionStudyRecordMapper.selectList(SessionStudyRecordDO::getSessionId, sessionId);
    }

}