package com.nnnmkj.thai.module.learning.dal.mysql.wordset;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetPageDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学习集 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordSetMapperPage extends BaseMapperX<WordSetPageDTO> {

    default PageResult<WordSetPageDTO> selectPage(WordSetPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<WordSetPageDTO> wrapper = new MPJLambdaWrapperX<WordSetPageDTO>()
                .selectAll(WordSetDO.class)
                .selectAs("t1.nickname", WordSetPageDTO::getNickname)
                .likeIfPresent(WordSetDO::getTitle, reqVO.getTitle())
                .eqIfPresent(WordSetDO::getVisibility, reqVO.getVisibility())
                .likeIfPresent(WordSetDO::getCreator, creatorQuery)
                .betweenIfPresent(WordSetDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WordSetDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)");
        return selectPage(reqVO, wrapper);
    }

}