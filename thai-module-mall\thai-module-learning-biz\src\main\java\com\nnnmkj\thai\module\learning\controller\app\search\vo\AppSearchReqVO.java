package com.nnnmkj.thai.module.learning.controller.app.search.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "用户 APP - 学习集搜索 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSearchReqVO extends PageParam {

    @Schema(description = "搜索关键字", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "搜索关键字不能为空")
    private String keyword;

    @Schema(description = "用户ID", example = "1024")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

}