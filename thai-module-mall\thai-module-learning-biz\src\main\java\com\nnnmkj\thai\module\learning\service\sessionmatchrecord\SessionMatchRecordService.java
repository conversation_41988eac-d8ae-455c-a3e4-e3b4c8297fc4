package com.nnnmkj.thai.module.learning.service.sessionmatchrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDTO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 配对记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SessionMatchRecordService {

    /**
     * 创建配对记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSessionMatchRecord(@Valid SessionMatchRecordSaveReqVO createReqVO);

    /**
     * 更新配对记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSessionMatchRecord(@Valid SessionMatchRecordSaveReqVO updateReqVO);

    /**
     * 删除配对记录
     *
     * @param id 编号
     */
    void deleteSessionMatchRecord(Long id);

    /**
     * 获得配对记录
     *
     * @param id 编号
     * @return 配对记录
     */
    SessionMatchRecordDO getSessionMatchRecord(Long id);

    /**
     * 获得配对记录分页
     *
     * @param pageReqVO 分页查询
     * @return 配对记录分页
     */
    PageResult<SessionMatchRecordDTO> getSessionMatchRecordPage(SessionMatchRecordPageReqVO pageReqVO);

    /**
     * 获得配对记录列表
     * @param sessionId 学习会话编号
     * @return 配对记录列表
     */
    List<SessionMatchRecordDO> getSessionMatchRecordList(Long sessionId);

}