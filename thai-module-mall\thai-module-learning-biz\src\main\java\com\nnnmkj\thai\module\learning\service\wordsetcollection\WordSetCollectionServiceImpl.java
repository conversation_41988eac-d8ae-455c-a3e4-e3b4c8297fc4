package com.nnnmkj.thai.module.learning.service.wordsetcollection;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollection.WordSetCollectionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollection.WordSetCollectionMapperX;
import com.nnnmkj.thai.module.learning.service.wordsetcollectionstatistic.WordSetCollectionStatisticService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.WORD_SET_COLLECTION_NOT_EXISTS;

/**
 * 学习集收藏 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordSetCollectionServiceImpl implements WordSetCollectionService {

    @Resource
    private WordSetCollectionMapper wordSetCollectionMapper;

    @Resource
    private WordSetCollectionMapperX wordSetCollectionMapperX;
    
    @Resource
    private WordSetCollectionStatisticService wordSetCollectionStatisticService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createWordSetCollection(WordSetCollectionSaveReqVO createReqVO) {
        // 插入
        WordSetCollectionDO wordSetCollection = BeanUtils.toBean(createReqVO, WordSetCollectionDO.class);
        wordSetCollectionMapper.insert(wordSetCollection);
        
        // 更新收藏统计
        wordSetCollectionStatisticService.incrWordSetCollectionCount(wordSetCollection.getSetId());
        
        // 返回
        return wordSetCollection.getId();
    }

    @Override
    public void updateWordSetCollection(WordSetCollectionSaveReqVO updateReqVO) {
        // 校验存在
        validateWordSetCollectionExists(updateReqVO.getId());
        // 更新
        WordSetCollectionDO updateObj = BeanUtils.toBean(updateReqVO, WordSetCollectionDO.class);
        wordSetCollectionMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWordSetCollection(Long id) {
        // 校验存在
        WordSetCollectionDO wordSetCollection = validateWordSetCollectionExists(id);
        // 删除
        wordSetCollectionMapper.deleteById(id);
        // 更新收藏统计
        wordSetCollectionStatisticService.decrWordSetCollectionCount(wordSetCollection.getSetId());
    }

    private WordSetCollectionDO validateWordSetCollectionExists(Long id) {
        WordSetCollectionDO wordSetCollection = wordSetCollectionMapper.selectById(id);
        if (wordSetCollection == null) {
            throw exception(WORD_SET_COLLECTION_NOT_EXISTS);
        }
        return wordSetCollection;
    }

    @Override
    public WordSetCollectionDO getWordSetCollection(Long id) {
        return wordSetCollectionMapper.selectById(id);
    }

    @Override
    public PageResult<WordSetCollectionDTO> getWordSetCollectionPage(WordSetCollectionPageReqVO pageReqVO) {
        return wordSetCollectionMapperX.selectPage(pageReqVO);
    }
}