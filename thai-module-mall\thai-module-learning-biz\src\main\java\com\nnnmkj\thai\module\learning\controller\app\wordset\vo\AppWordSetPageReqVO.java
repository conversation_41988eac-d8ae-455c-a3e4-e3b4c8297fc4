package com.nnnmkj.thai.module.learning.controller.app.wordset.vo;

import com.nnnmkj.thai.framework.common.pojo.SortablePageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 学习集分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppWordSetPageReqVO extends SortablePageParam {

    @Schema(description = "标题（说明）")
    private String title;

    @Schema(description = "可见范围")
    private Integer visibility;

    @Schema(description = "用户ID", example = "30890")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "排序字段，可选值：1-收藏数, 2-学习次数, 3-点击量, 4-更新时间")
    private Integer sortField;

    @Schema(description = "排序方式，可选值：asc-升序, desc-降序，默认为 desc")
    private String sortOrder;


}