package com.nnnmkj.thai.module.learning.service.sessiontestrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.sessiontestrecord.SessionTestRecordMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.sessiontestrecord.SessionTestRecordMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.SESSION_TEST_RECORD_NOT_EXISTS;

/**
 * 测试记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SessionTestRecordServiceImpl implements SessionTestRecordService {

    @Resource
    private SessionTestRecordMapper sessionTestRecordMapper;

    @Resource
    private SessionTestRecordMapperX sessionTestRecordMapperX;

    @Override
    public Long createSessionTestRecord(SessionTestRecordSaveReqVO createReqVO) {
        // 插入
        SessionTestRecordDO sessionTestRecord = BeanUtils.toBean(createReqVO, SessionTestRecordDO.class);
        sessionTestRecordMapper.insert(sessionTestRecord);
        // 返回
        return sessionTestRecord.getId();
    }

    @Override
    public void updateSessionTestRecord(SessionTestRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateSessionTestRecordExists(updateReqVO.getId());
        // 更新
        SessionTestRecordDO updateObj = BeanUtils.toBean(updateReqVO, SessionTestRecordDO.class);
        sessionTestRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteSessionTestRecord(Long id) {
        // 校验存在
        validateSessionTestRecordExists(id);
        // 删除
        sessionTestRecordMapper.deleteById(id);
    }

    private void validateSessionTestRecordExists(Long id) {
        if (sessionTestRecordMapper.selectById(id) == null) {
            throw exception(SESSION_TEST_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public SessionTestRecordDO getSessionTestRecord(Long id) {
        return sessionTestRecordMapper.selectById(id);
    }

    @Override
    public PageResult<SessionTestRecordDTO> getSessionTestRecordPage(SessionTestRecordPageReqVO pageReqVO) {
        return sessionTestRecordMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<SessionTestRecordDO> getSessionTestRecordList(Long sessionId) {
        return sessionTestRecordMapper.selectList(SessionTestRecordDO::getSessionId, sessionId);
    }

}