package com.nnnmkj.thai.module.learning.controller.app.session.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 学习会话分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppSessionPageReqVO extends PageParam {

    @Schema(description = "学习集编号", example = "10450")
    private Long setId;

    @Schema(description = "用户编号", example = "19826")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;


    @Schema(description = "学习模式")
    private Integer mode;

    @Schema(description = "会话状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}