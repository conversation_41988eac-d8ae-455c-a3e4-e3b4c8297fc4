package com.nnnmkj.thai.module.learning.dal.dataobject.wordset;

import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 学习集 DTO
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode
public class WordSetDTO extends WordSetDO {
    /**
     * 是否收藏
     */
    private Integer isStore;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 状态：0-未完成，1-已完成
     */
    private Integer status;
    /**
     * 单词卡数量
     */
    private Long wordCardCount;
    /**
     * 单词卡列表
     */
    private List<WordCardDTO> wordCards;

    private Integer collectionCount;

    private Integer studyCount;

    private Integer viewCount;
}