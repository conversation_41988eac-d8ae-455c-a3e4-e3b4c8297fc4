package com.nnnmkj.thai.module.learning.service.sessionresult;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDTO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 会话成绩 Service 接口
 *
 * <AUTHOR>
 */
public interface SessionResultService {

    /**
     * 创建会话成绩
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSessionResult(@Valid SessionResultSaveReqVO createReqVO);

    /**
     * 更新会话成绩
     *
     * @param updateReqVO 更新信息
     */
    void updateSessionResult(@Valid SessionResultSaveReqVO updateReqVO);

    /**
     * 删除会话成绩
     *
     * @param id 编号
     */
    void deleteSessionResult(Long id);

    /**
     * 获得会话成绩
     *
     * @param id 编号
     * @return 会话成绩
     */
    SessionResultDO getSessionResult(Long id);

    /**
     * 获得会话成绩分页
     *
     * @param pageReqVO 分页查询
     * @return 会话成绩分页
     */
    PageResult<SessionResultDTO> getSessionResultPage(SessionResultPageReqVO pageReqVO);

    /**
     * 获得会话成绩列表
     *
     * @param sessionId 会话ID
     * @return 会话成绩列表
     */
    List<SessionResultDO> getSessionResultList(Long sessionId);



}