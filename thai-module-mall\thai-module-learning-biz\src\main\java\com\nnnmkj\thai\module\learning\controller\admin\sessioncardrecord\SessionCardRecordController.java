package com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDTO;
import com.nnnmkj.thai.module.learning.service.sessioncardrecord.SessionCardRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 单词卡记录")
@RestController
@RequestMapping("/learning/session-card-record")
@Validated
public class SessionCardRecordController {

    @Resource
    private SessionCardRecordService sessionCardRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建单词卡记录")
    @PreAuthorize("@ss.hasPermission('learning:session-card-record:create')")
    public CommonResult<Long> createSessionCardRecord(@Valid @RequestBody SessionCardRecordSaveReqVO createReqVO) {
        return success(sessionCardRecordService.createSessionCardRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新单词卡记录")
    @PreAuthorize("@ss.hasPermission('learning:session-card-record:update')")
    public CommonResult<Boolean> updateSessionCardRecord(@Valid @RequestBody SessionCardRecordSaveReqVO updateReqVO) {
        sessionCardRecordService.updateSessionCardRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除单词卡记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:session-card-record:delete')")
    public CommonResult<Boolean> deleteSessionCardRecord(@RequestParam("id") Long id) {
        sessionCardRecordService.deleteSessionCardRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得单词卡记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:session-card-record:query')")
    public CommonResult<SessionCardRecordRespVO> getSessionCardRecord(@RequestParam("id") Long id) {
        SessionCardRecordDO sessionCardRecord = sessionCardRecordService.getSessionCardRecord(id);
        return success(BeanUtils.toBean(sessionCardRecord, SessionCardRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得单词卡记录分页")
    @PreAuthorize("@ss.hasPermission('learning:session-card-record:query')")
    public CommonResult<PageResult<SessionCardRecordRespVO>> getSessionCardRecordPage(@Valid SessionCardRecordPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<SessionCardRecordDTO> pageResult = sessionCardRecordService.getSessionCardRecordPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, SessionCardRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出单词卡记录 Excel")
    @PreAuthorize("@ss.hasPermission('learning:session-card-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSessionCardRecordExcel(@Valid SessionCardRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SessionCardRecordDTO> list = sessionCardRecordService.getSessionCardRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "单词卡记录.xls", "数据", SessionCardRecordRespVO.class,
                        BeanUtils.toBean(list, SessionCardRecordRespVO.class));
    }

}