package com.nnnmkj.thai.module.learning.service.search;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.app.search.vo.AppSearchReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetRespVO;
import com.nnnmkj.thai.module.learning.convert.wordset.WordSetConvert;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapperX;
import com.nnnmkj.thai.module.learning.service.wordsetcollection.AppWordSetCollectionService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * App 搜索 Service 实现类
 *
 * <AUTHOR> AI
 */
@Service
@Slf4j
public class AppSearchServiceImpl implements AppSearchService {

    @Resource
    private WordSetMapperX wordSetMapperX;
    @Resource
    private MemberUserApi memberUserApi;
    @Resource
    private AppWordSetCollectionService wordSetCollectionService;

    @Override
    public PageResult<AppWordSetRespVO> searchLearning(AppSearchReqVO reqVO) {
        MemberUserRespDTO loginUser = memberUserApi.getLoginUser();
        // 搜索学习集
        PageResult<WordSetDTO> wordSetPage = wordSetMapperX.selectUserVisiblePage(reqVO, loginUser);
        List<WordSetDTO> wordSetPageList = wordSetPage.getList();
        // 处理是否收藏返回值
        Set<Long> setIds = wordSetPageList.stream().map(WordSetDTO::getId).collect(Collectors.toSet());
        List<WordSetCollectionDO> wordSetCollectionList = wordSetCollectionService.getWordSetCollectionList(setIds, reqVO.getUserId(), reqVO.getUserType());
        Set<Long> storeSetIds = wordSetCollectionList.stream().map(WordSetCollectionDO::getSetId).collect(Collectors.toSet());
        return WordSetConvert.INSTANCE.convertAppPage(wordSetPage, storeSetIds);
    }
}