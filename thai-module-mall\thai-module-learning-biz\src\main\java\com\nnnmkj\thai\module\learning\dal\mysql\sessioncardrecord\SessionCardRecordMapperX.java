package com.nnnmkj.thai.module.learning.dal.mysql.sessioncardrecord;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 单词卡记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionCardRecordMapperX extends BaseMapperX<SessionCardRecordDTO> {

    default PageResult<SessionCardRecordDTO> selectPage(SessionCardRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<SessionCardRecordDTO> wrapper = new MPJLambdaWrapperX<SessionCardRecordDTO>()
                .selectAll(SessionCardRecordDO.class)
                .selectAs("t1.nickname", SessionCardRecordDTO::getNickname)
                .selectAs(WordSetDO::getTitle, SessionCardRecordDTO::getSetTitle)
                .eqIfPresent(SessionCardRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionCardRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionCardRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionCardRecordDO::getWordCardId, reqVO.getWordCardId())
                .eqIfPresent(SessionCardRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SessionCardRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionCardRecordDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, SessionCardRecordDO::getSetId);
        return selectPage(reqVO, wrapper);
    }

}