package com.nnnmkj.thai.module.learning.service.sessiontestrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo.SessionTestRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessiontestrecord.SessionTestRecordDTO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 测试记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SessionTestRecordService {

    /**
     * 创建测试记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSessionTestRecord(@Valid SessionTestRecordSaveReqVO createReqVO);

    /**
     * 更新测试记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSessionTestRecord(@Valid SessionTestRecordSaveReqVO updateReqVO);

    /**
     * 删除测试记录
     *
     * @param id 编号
     */
    void deleteSessionTestRecord(Long id);

    /**
     * 获得测试记录
     *
     * @param id 编号
     * @return 测试记录
     */
    SessionTestRecordDO getSessionTestRecord(Long id);

    /**
     * 获得测试记录分页
     *
     * @param pageReqVO 分页查询
     * @return 测试记录分页
     */
    PageResult<SessionTestRecordDTO> getSessionTestRecordPage(SessionTestRecordPageReqVO pageReqVO);

    /**
     * 获得测试记录列表
     * @param sessionId 会话编号
     * @return 测试记录列表
     */
    List<SessionTestRecordDO> getSessionTestRecordList(Long sessionId);

}