package com.nnnmkj.thai.module.quiz.enums;

/**
 * quiz 字典类型的枚举类
 * <AUTHOR>
 */
public interface DictTypeConstants {

    // ========== 题库字典类型常量 ==========
    String QUESTION_TYPE = "assignment_question_type"; // 题目类型
    String QUESTION_DIFFICULTY = "assignment_question_difficulty"; // 难易度

    String AUDIT_STATUS = "audit_status"; // 审核状态
    String TASK_STATUS = "task_status"; // 任务状态
    String TASK_RECORD_STATUS = "task_record_status"; // 任务记录状态
    String CREATE_METHOD = "create_method"; // 创建方式
}
