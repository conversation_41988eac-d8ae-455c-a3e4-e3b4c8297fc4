package com.nnnmkj.thai.module.learning.controller.admin.wordcard.vo;

import com.nnnmkj.thai.module.learning.dal.dataobject.wordcarddefinition.WordCardDefinitionDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 单词卡新增/修改 Request VO")
@Data
public class WordCardSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2435")
    private Long id;

    @Schema(description = "学习集编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15960")
    @NotNull(message = "学习集编号不能为空")
    private Long wordSetId;

    @Schema(description = "音标")
    private String phoneticSymbol;

    @Schema(description = "词语", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "词语不能为空")
    private String word;

    @Schema(description = "图片", example = "https://www.iocoder.cn")
    private String imageUrl;

    @Schema(description = "音频", example = "https://www.iocoder.cn")
    private String audioUrl;

    @Schema(description = "单词卡定义列表")
    private List<WordCardDefinitionDO> wordCardDefinitions;

}