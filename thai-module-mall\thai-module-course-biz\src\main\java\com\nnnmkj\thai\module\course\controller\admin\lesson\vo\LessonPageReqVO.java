package com.nnnmkj.thai.module.course.controller.admin.lesson.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LessonPageReqVO extends PageParam {

    @Schema(description = "课程标题")
    private String title;

    @Schema(description = "课程简介", example = "你猜")
    private String description;

    @Schema(description = "课程封面图URL")
    private String coverImage;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}