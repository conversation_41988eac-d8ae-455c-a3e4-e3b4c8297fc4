package com.nnnmkj.thai.module.learning.dal.mysql.sessioncardrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 单词卡记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionCardRecordMapper extends BaseMapperX<SessionCardRecordDO> {

    default PageResult<SessionCardRecordDO> selectPage(SessionCardRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<SessionCardRecordDO>()
                .eqIfPresent(SessionCardRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionCardRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionCardRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionCardRecordDO::getWordCardId, reqVO.getWordCardId())
                .eqIfPresent(SessionCardRecordDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(SessionCardRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionCardRecordDO::getId));
    }

}