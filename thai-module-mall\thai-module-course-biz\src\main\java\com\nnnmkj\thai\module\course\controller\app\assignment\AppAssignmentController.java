package com.nnnmkj.thai.module.course.controller.app.assignment;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentPageReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentRespVO;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentSaveReqVO;
import com.nnnmkj.thai.module.course.convert.assignment.AssignmentConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import com.nnnmkj.thai.module.course.service.assignment.AssignmentService;
import com.nnnmkj.thai.module.course.service.assignmentquestion.AssignmentQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 App - 作业库")
@RestController
@RequestMapping("/course/assignment")
@Validated
public class AppAssignmentController {

    @Resource
    private AssignmentService assignmentService;
    @Resource
    private AssignmentQuestionService questionService;

    @GetMapping("/page")
    @Operation(summary = "获得课程作业分页")
    public CommonResult<PageResult<AppAssignmentRespVO>> getAssignmentPage(@Valid AppAssignmentPageReqVO pageReqVO) {
        PageResult<AssignmentDO> pageResult = assignmentService.getAssignmentPage(pageReqVO);
        List<AssignmentDO> resultList = pageResult.getList();
        if (CollUtil.isEmpty(resultList)) {
            return success(PageResult.empty());
        }

        List<Long> assignmentIds = CollectionUtils.convertList(resultList, AssignmentDO::getId);
        List<AssignmentQuestionDO> questions = questionService.getAssignmentQuestionListByAssignmentIds(assignmentIds);
        return success(AssignmentConvert.INSTANCE.convertPage(pageResult, questions));
    }

    @PostMapping("/create")
    @Operation(summary = "创建课程作业")
    public CommonResult<Long> createAssignment(@Valid @RequestBody AppAssignmentSaveReqVO createReqVO) {
        return success(assignmentService.createAssignment(getLoginUserId(), createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业")
    public CommonResult<Boolean> updateAssignment(@Valid @RequestBody AppAssignmentSaveReqVO updateReqVO) {
        assignmentService.updateAssignment(getLoginUserId(), updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业")
    public CommonResult<Boolean> deleteAssignment(@RequestParam("id") Long id) {
        assignmentService.deleteAssignment(getLoginUserId(), id);
        return success(true);
    }
}
