package com.nnnmkj.thai.module.learning.service.session;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.question.QuestionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionquestion.SessionQuestionDO;
import com.nnnmkj.thai.module.learning.dal.mysql.question.QuestionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.session.SessionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.session.SessionMapperX;
import com.nnnmkj.thai.module.learning.dal.mysql.sessionquestion.SessionQuestionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsetstudystatistic.WordSetStudyStatisticMapper;
import com.nnnmkj.thai.module.learning.enums.StudyModeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.WORD_SET_COLLECTION_NOT_EXISTS;

/**
 * 学习会话 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SessionServiceImpl implements SessionService {

    @Resource
    private SessionMapper sessionMapper;

    @Resource
    private SessionMapperX sessionMapperX;

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private SessionQuestionMapper sessionQuestionMapper;

    @Resource
    private WordSetStudyStatisticMapper wordSetStudyStatisticMapper;

    // 随机取得的题目数量
    private static final Integer RANDOM_QUESTION_COUNT = 10;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSession(SessionSaveReqVO createReqVO) {
        // 插入
        SessionDO session = BeanUtils.toBean(createReqVO, SessionDO.class);
        sessionMapper.insert(session);
        Long sessionId = session.getId();
        Long setId = session.getSetId();
        Integer mode = session.getMode();
        // 如果学习模式为学习、测试，则插入学习会话题目关联表
        if (StudyModeEnum.LEARNING.getType().equals(mode) || StudyModeEnum.TESTING.getType().equals(mode)) {
            // 随机从题目表选择10道题
            LambdaQueryWrapper<QuestionDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QuestionDO::getSetId, setId).last("ORDER BY RAND() LIMIT " + RANDOM_QUESTION_COUNT);
            List<QuestionDO> questionDOS = questionMapper.selectList(wrapper);
            if (CollUtil.isNotEmpty(questionDOS)) {
                List<SessionQuestionDO> sessionQuestionDOS = new ArrayList<>();
                for (QuestionDO questionDO : questionDOS) {
                    SessionQuestionDO sessionQuestion = new SessionQuestionDO();
                    sessionQuestion.setSessionId(sessionId);
                    sessionQuestion.setQuestionId(questionDO.getId());
                    sessionQuestionDOS.add(sessionQuestion);
                }
                sessionQuestionMapper.insertBatch(sessionQuestionDOS);
            }
        }
        // 更新学习集学习统计
        wordSetStudyStatisticMapper.atomicUpdateCount(setId, 1);
        // 返回
        return session.getId();
    }

    @Override
    public void updateSession(SessionSaveReqVO updateReqVO) {
        // 校验存在
        validateSessionExists(updateReqVO.getId());
        // 更新
        SessionDO updateObj = BeanUtils.toBean(updateReqVO, SessionDO.class);
        sessionMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSession(Long id) {
        // 校验存在
        validateSessionExists(id);
        // 删除
        sessionMapper.deleteById(id);
    }

    private void validateSessionExists(Long id) {
        SessionDO session = sessionMapper.selectById(id);
        if (session == null) {
            throw exception(WORD_SET_COLLECTION_NOT_EXISTS);
        }
    }

    @Override
    public SessionDO getSession(Long id) {
        return sessionMapper.selectById(id);
    }

    @Override
    public PageResult<SessionDTO> getSessionPage(SessionPageReqVO pageReqVO) {
        return sessionMapperX.selectPage(pageReqVO);
    }

}