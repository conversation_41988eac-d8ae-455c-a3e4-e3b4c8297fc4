package com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDTO;
import com.nnnmkj.thai.module.learning.service.sessionmatchrecord.SessionMatchRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 配对记录")
@RestController
@RequestMapping("/learning/session-match-record")
@Validated
public class SessionMatchRecordController {

    @Resource
    private SessionMatchRecordService sessionMatchRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建配对记录")
    @PreAuthorize("@ss.hasPermission('learning:session-match-record:create')")
    public CommonResult<Long> createSessionMatchRecord(@Valid @RequestBody SessionMatchRecordSaveReqVO createReqVO) {
        return success(sessionMatchRecordService.createSessionMatchRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新配对记录")
    @PreAuthorize("@ss.hasPermission('learning:session-match-record:update')")
    public CommonResult<Boolean> updateSessionMatchRecord(@Valid @RequestBody SessionMatchRecordSaveReqVO updateReqVO) {
        sessionMatchRecordService.updateSessionMatchRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除配对记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:session-match-record:delete')")
    public CommonResult<Boolean> deleteSessionMatchRecord(@RequestParam("id") Long id) {
        sessionMatchRecordService.deleteSessionMatchRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得配对记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:session-match-record:query')")
    public CommonResult<SessionMatchRecordRespVO> getSessionMatchRecord(@RequestParam("id") Long id) {
        SessionMatchRecordDO sessionMatchRecord = sessionMatchRecordService.getSessionMatchRecord(id);
        return success(BeanUtils.toBean(sessionMatchRecord, SessionMatchRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得配对记录分页")
    @PreAuthorize("@ss.hasPermission('learning:session-match-record:query')")
    public CommonResult<PageResult<SessionMatchRecordRespVO>> getSessionMatchRecordPage(@Valid SessionMatchRecordPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<SessionMatchRecordDTO> pageResult = sessionMatchRecordService.getSessionMatchRecordPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, SessionMatchRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出配对记录 Excel")
    @PreAuthorize("@ss.hasPermission('learning:session-match-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSessionMatchRecordExcel(@Valid SessionMatchRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        List<SessionMatchRecordDTO> list = sessionMatchRecordService.getSessionMatchRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "配对记录.xls", "数据", SessionMatchRecordRespVO.class,
                        BeanUtils.toBean(list, SessionMatchRecordRespVO.class));
    }

}