package com.nnnmkj.thai.module.course.service.lesson;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonAttachmentMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonMapper;
import com.nnnmkj.thai.module.course.dal.mysql.lesson.LessonMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_NOT_EXISTS;

/**
 * 课程 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LessonServiceImpl implements LessonService {

    @Resource
    private LessonMapper lessonMapper;

    @Resource
    private LessonMapperX lessonMapperX;
    @Resource
    private LessonAttachmentMapper lessonAttachmentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLesson(LessonSaveReqVO createReqVO) {
        // 插入
        LessonDO lesson = BeanUtils.toBean(createReqVO, LessonDO.class);
        lessonMapper.insert(lesson);

        // 插入子表
        createLessonAttachmentList(lesson.getId(), createReqVO.getLessonAttachments());
        // 返回
        return lesson.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLesson(LessonSaveReqVO updateReqVO) {
        // 校验存在
        validateLessonExists(updateReqVO.getId());
        // 更新
        LessonDO updateObj = BeanUtils.toBean(updateReqVO, LessonDO.class);
        lessonMapper.updateById(updateObj);

        // 更新子表
        updateLessonAttachmentList(updateReqVO.getId(), updateReqVO.getLessonAttachments());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLesson(Long id) {
        // 校验存在
        validateLessonExists(id);
        // 删除
        lessonMapper.deleteById(id);

        // 删除子表
        deleteLessonAttachmentByCourseId(id);
    }

    private void validateLessonExists(Long id) {
        if (lessonMapper.selectById(id) == null) {
            throw exception(LESSON_NOT_EXISTS);
        }
    }

    @Override
    public LessonDO getLesson(Long id) {
        return lessonMapper.selectById(id);
    }

    @Override
    public PageResult<LessonDTO> getLessonPage(LessonPageReqVO pageReqVO) {
        return lessonMapperX.selectPage(pageReqVO);
    }

    @Override
    public PageResult<LessonDTO> getLessonPage(Long userId, Integer userType, LessonPageReqVO pageReqVO) {
        return lessonMapperX.selectPageByCreator(userId, userType, pageReqVO);
    }

    @Override
    public List<LessonDO> getLessonList(LessonPageReqVO pageReqVO) {
        return lessonMapper.selectList(pageReqVO);
    }

    @Override
    public List<LessonDO> getLessonList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return lessonMapper.selectBatchIds(ids);
    }

    // ==================== 子表（课程附件） ====================

    @Override
    public List<LessonAttachmentDO> getLessonAttachmentListByCourseId(Long courseId) {
        return lessonAttachmentMapper.selectListByCourseId(courseId);
    }

    private void createLessonAttachmentList(Long id, List<LessonAttachmentDO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(o -> o.setCourseId(id));
        lessonAttachmentMapper.insertBatch(list);
    }

    private void updateLessonAttachmentList(Long id, List<LessonAttachmentDO> list) {
        // 删除旧数据
        lessonAttachmentMapper.deleteByCourseId(id);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        // 添加新数据前重置id、updater和updateTime，避免主键冲突
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null));
        createLessonAttachmentList(id, list);
    }

    private void deleteLessonAttachmentByCourseId(Long id) {
        lessonAttachmentMapper.deleteByCourseId(id);
    }
}