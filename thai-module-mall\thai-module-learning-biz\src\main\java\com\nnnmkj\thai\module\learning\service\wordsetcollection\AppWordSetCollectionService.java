package com.nnnmkj.thai.module.learning.service.wordsetcollection;

import com.nnnmkj.thai.module.learning.controller.app.wordsetcollection.vo.AppWordSetCollectionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 学习集收藏 Service 接口
 *
 * <AUTHOR>
 */
public interface AppWordSetCollectionService {

    /**
     * 创建学习集收藏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWordSetCollection(@Valid AppWordSetCollectionSaveReqVO createReqVO);

    /**
     * 删除学习集收藏
     *
     * @param id 编号
     * @param userId 用户编号
     */
    void deleteWordSetCollection(Long id, Long userId);

    /**
     * 获得学习集收藏列表
     *
     * @param setIds 编号
     * @param userId 用户编号
     * @param userType 用户类型
     * @return 学习集收藏
     */
    List<WordSetCollectionDO> getWordSetCollectionList(Collection<Long> setIds, Long userId, Integer userType);

}