package com.nnnmkj.thai.module.learning.dal.mysql.sessionresult;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessionresult.vo.SessionResultPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionresult.SessionResultDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 会话成绩 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionResultMapperX extends BaseMapperX<SessionResultDTO> {

    default PageResult<SessionResultDTO> selectPage(SessionResultPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<SessionResultDTO> wrapper = new MPJLambdaWrapperX<SessionResultDTO>()
                .selectAll(SessionResultDO.class)
                .selectAs("t1.nickname", SessionResultDTO::getNickname)
                .selectAs(WordSetDO::getTitle, SessionResultDTO::getSetTitle)
                .eqIfPresent(SessionResultDO::getSessionId, reqVO.getSessionId())
                .likeIfPresent(SessionResultDO::getCreator, creatorQuery)
                .eqIfPresent(SessionResultDO::getSetId, reqVO.getSetId())
                .eqIfPresent(SessionResultDO::getTimeTaken, reqVO.getTimeTaken())
                .eqIfPresent(SessionResultDO::getAllCount, reqVO.getAllCount())
                .eqIfPresent(SessionResultDO::getCorrectCount, reqVO.getCorrectCount())
                .eqIfPresent(SessionResultDO::getErrorCount, reqVO.getErrorCount())
                .betweenIfPresent(SessionResultDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionResultDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .leftJoin(WordSetDO.class, "t2", WordSetDO::getId, SessionResultDO::getSetId);
        return selectPage(reqVO, wrapper);
    }

}