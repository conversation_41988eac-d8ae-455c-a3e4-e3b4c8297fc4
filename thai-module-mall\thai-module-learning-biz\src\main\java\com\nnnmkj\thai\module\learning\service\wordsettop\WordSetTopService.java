package com.nnnmkj.thai.module.learning.service.wordsettop;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDTO;
import jakarta.validation.Valid;

/**
 * 学习集置顶 Service 接口
 *
 * <AUTHOR>
 */
public interface WordSetTopService {

    /**
     * 创建学习集置顶
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWordSetTop(@Valid WordSetTopSaveReqVO createReqVO);

    /**
     * 更新学习集置顶
     *
     * @param updateReqVO 更新信息
     */
    void updateWordSetTop(@Valid WordSetTopSaveReqVO updateReqVO);

    /**
     * 删除学习集置顶
     *
     * @param id 编号
     */
    void deleteWordSetTop(Long id);

    /**
     * 获得学习集置顶
     *
     * @param id 编号
     * @return 学习集置顶
     */
    WordSetTopDO getWordSetTop(Long id);

    /**
     * 获得学习集置顶分页
     *
     * @param pageReqVO 分页查询
     * @return 学习集置顶分页
     */
    PageResult<WordSetTopDTO> getWordSetTopPage(WordSetTopPageReqVO pageReqVO);

}