package com.nnnmkj.thai.module.learning.dal.mysql.wordset;

import cn.hutool.core.collection.CollUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.learning.controller.app.search.vo.AppSearchReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetClassPageReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetFavoritePageReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.certification.MemberCertificationDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.membergroupwordset.MemberGroupWordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollectionstatistic.WordSetCollectionStatisticDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetstudystatistic.WordSetStudyStatisticDO;
import com.nnnmkj.thai.module.learning.enums.StudyModeEnum;
import com.nnnmkj.thai.module.learning.enums.WordSetVisibilityEnum;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import com.nnnmkj.thai.module.member.enums.CertificationStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 学习集 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WordSetMapperX extends BaseMapperX<WordSetDTO> {

    default PageResult<WordSetDTO> selectPage(AppWordSetPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<WordSetDTO> wrapper = new MPJLambdaWrapperX<WordSetDTO>()
                .selectAll(WordSetDO.class)
                .selectAs("t1.nickname", WordSetDTO::getNickname)
                .selectAs("t1.avatar", WordSetDTO::getAvatar)
                .likeIfPresent(WordSetDO::getCreator, creatorQuery)
                .eqIfPresent(WordSetDO::getVisibility, reqVO.getVisibility())
                .eqIfPresent(WordSetDO::getTitle, reqVO.getTitle())
                .betweenIfPresent(WordSetDO::getCreateTime, reqVO.getCreateTime())
                .leftJoin("member_user t1 ON (t.creator LIKE concat('%mu_id:', t1.id , '|%') AND t1.deleted = 0)")
                .selectSub(
                        WordCardDO.class,
                        subWrapper -> subWrapper.selectCount(WordCardDO::getId).eq(WordCardDO::getWordSetId, WordSetDO::getId),
                        WordSetDTO::getWordCardCount
                )
                .orderByDesc(WordSetDO::getId);
        return selectPage(reqVO, wrapper);
    }

    default PageResult<WordSetDTO> getFavoriteWordSetPage(AppWordSetFavoritePageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new MPJLambdaWrapperX<WordSetDTO>()
                .selectAll(WordSetDO.class)
                .selectAs("t1.nickname", WordSetDTO::getNickname)
                .selectAs("t1.avatar", WordSetDTO::getAvatar)
                .leftJoin("member_user t1 ON (t.creator LIKE concat('%mu_id:', t1.id , '|%') AND t1.deleted = 0)")
                .selectSub(
                        WordCardDO.class,
                        subWrapper -> subWrapper.selectCount(WordCardDO::getId).eq(WordCardDO::getWordSetId, WordSetDO::getId),
                        WordSetDTO::getWordCardCount
                )
                .leftJoin(WordSetCollectionDO.class, "wsc", WordSetCollectionDO::getSetId, WordSetDO::getId)
                .likeIfExists(WordSetCollectionDO::getCreator, creatorQuery)
                .orderByDesc(WordSetDO::getCreateTime));
    }

    default PageResult<WordSetDTO> getClassWordSetPage(AppWordSetClassPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        // 构建基础查询条件
        MPJLambdaWrapper<WordSetDTO> wrapper = buildBaseQuery(reqVO);
        // 添加认证条件
        if (reqVO.getStatus() != null && reqVO.getStatus() >= 0 && reqVO.getStatus() <= 1) {
            wrapper.leftJoin(MemberCertificationDO.class, "mc", on -> on
                    .likeIfExists(MemberCertificationDO::getCreator, creatorQuery)
                    .eq(MemberCertificationDO::getStatus, CertificationStatusEnum.APPROVED.getStatus()));
            addCertificationCondition(wrapper, reqVO.getStatus());
        }

        return selectPage(reqVO, wrapper);
    }

    default PageResult<WordSetDTO> selectUserVisiblePage(AppSearchReqVO searchReqVO, MemberUserRespDTO user) {
        List<Long> groupIds = user.getGroupIds();
        String creatorQuery = CommonUtils.getCreatorQuery(searchReqVO.getUserId(), searchReqVO.getUserType());
        MPJLambdaWrapper<WordSetDTO> wrapper = new MPJLambdaWrapperX<WordSetDTO>()
                .selectAll(WordSetDO.class)
                .selectAs("t1.nickname", WordSetDTO::getNickname)
                .selectAs("t1.avatar", WordSetDTO::getAvatar)
                .leftJoin("member_user t1 ON (t.creator LIKE concat('%mu_id:', t1.id , '|%') AND t1.deleted = 0)")
                .and(w -> w
                        // 用户自己创建的学习集
                        .likeIfExists(WordSetDO::getCreator, creatorQuery)
                        // 公开可见的学习集
                        .or(w2 -> w2
                                .eq(WordSetDO::getVisibility, WordSetVisibilityEnum.ALL.getType())
                        )
                        // 用户所在组可见的学习集
                        .or(CollUtil.isNotEmpty(groupIds), w3 -> w3
                                .and(w4 -> w4
                                        .eq(WordSetDO::getVisibility, WordSetVisibilityEnum.GROUP.getType())
                                        .in(WordSetDO::getId, groupIds)
                                )
                        )
                )
                // 根据搜索关键字模糊查询标题
                .likeIfExists(WordSetDO::getTitle, searchReqVO.getKeyword())
                // 子查询统计每个学习集下的单词卡数量
                .selectSub(
                        WordCardDO.class,
                        subWrapper ->
                                subWrapper.selectCount(WordCardDO::getId).eq(WordCardDO::getWordSetId, WordSetDO::getId),
                        WordSetDTO::getWordCardCount
                )
                // 按创建时间降序排序
                .orderByDesc(WordSetDO::getId);
        return selectPage(searchReqVO, wrapper);
    }

    default MPJLambdaWrapper<WordSetDTO> buildBaseQuery(AppWordSetClassPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return new MPJLambdaWrapperX<WordSetDTO>()
                .selectAll(WordSetDO.class)
                .selectAs("t1.nickname", WordSetDTO::getNickname)
                .selectAs("t1.avatar", WordSetDTO::getAvatar)
                .leftJoin("member_user t1 ON (t.creator LIKE concat('%mu_id:', t1.id , '|%') AND t1.deleted = 0)")
                .selectSub(
                        SessionDO.class,
                        subWrapper -> subWrapper
                                .select(SessionDO::getStatus)
                                .eq(SessionDO::getSetId, WordSetDO::getId)
                                .likeIfExists(SessionDO::getCreator, creatorQuery)
                                .eq(SessionDO::getMode, StudyModeEnum.WORD_CARD.getType())
                                .orderByAsc(SessionDO::getStatus)
                                .orderByDesc(SessionDO::getCreateTime)
                                .last("limit 1"),
                        WordSetDTO::getStatus
                )
                .leftJoin(MemberGroupWordSetDO.class, "mgws", MemberGroupWordSetDO::getWordSetId, WordSetDO::getId)
                .eq(MemberGroupWordSetDO::getGroupId, reqVO.getClassId())
                .ne(WordSetDO::getVisibility, WordSetVisibilityEnum.SELF.getType());
    }

    default void addCertificationCondition(MPJLambdaWrapper<WordSetDTO> wrapper, Integer status) {
        if (status == 0) {
            wrapper.isNotNull(MemberCertificationDO::getId);
        } else if (status == 1) {
            wrapper.isNull(MemberCertificationDO::getId);
        }
    }

    /**
     * 查询优质学习集
     *
     * @param pageReqVO 分页请求参数
     * @return 优质学习集列表分页结果
     */
    default PageResult<WordSetDTO> selectQualityWordSets(AppWordSetPageReqVO pageReqVO) {
        // 构建查询条件
        MPJLambdaWrapper<WordSetDTO> wrapper = new MPJLambdaWrapperX<WordSetDTO>()
                // 基础字段查询
                .selectAll(WordSetDO.class)
                .selectAs("t1.nickname", WordSetDTO::getNickname)
                .selectAs("t1.avatar", WordSetDTO::getAvatar)
                .leftJoin("member_user t1 ON (t.creator LIKE concat('%mu_id:', t1.id , '|%') AND t1.deleted = 0)")
                .eq(WordSetDO::getVisibility, WordSetVisibilityEnum.ALL.getType())
                .likeIfExists(WordSetDO::getTitle, pageReqVO.getTitle())
                // 单词卡数量统计
                .selectSub(
                        WordCardDO.class,
                        subWrapper -> subWrapper.selectCount(WordCardDO::getId)
                                .eq(WordCardDO::getWordSetId, WordSetDO::getId),
                        WordSetDTO::getWordCardCount
                )
                // 关联收藏统计
                .leftJoin(WordSetCollectionStatisticDO.class,
                        "wscs",
                        WordSetCollectionStatisticDO::getSetId, WordSetDO::getId)
                .selectAs(WordSetCollectionStatisticDO::getCount, "collectionCount")
                // 关联学习次数统计
                .leftJoin(WordSetStudyStatisticDO.class,
                        "wss",
                        WordSetStudyStatisticDO::getSetId, WordSetDO::getId)
                .selectAs(WordSetStudyStatisticDO::getCount, "studyCount")
                // 加权分数计算 (学习次数×0.3 + 收藏量×0.5 + 点击量×0.2)
                .select("IFNULL(t2.count, 0) * 0.3 + IFNULL(t1.count, 0) * 0.5 + IFNULL(t.view_count, 0) * 0.2 AS weightedScore");

        // 排序处理
        Integer sortField = pageReqVO.getSortField();
        if (sortField == null || sortField == 0) {
            wrapper.orderByDesc("weightedScore");
        } else {
            boolean isAsc = "asc".equalsIgnoreCase(pageReqVO.getSortOrder());
            switch (sortField) {
                case 1 -> wrapper.orderBy(true, isAsc, "t1.count");
                case 2 -> wrapper.orderBy(true, isAsc, "t2.count");
                case 3 -> wrapper.orderBy(true, isAsc, "t.view_count");
                case 4 -> wrapper.orderBy(true, isAsc, "t.update_time");
                default -> wrapper.orderByDesc("weightedScore");
            }
        }

        return selectPage(pageReqVO, wrapper);
    }

}