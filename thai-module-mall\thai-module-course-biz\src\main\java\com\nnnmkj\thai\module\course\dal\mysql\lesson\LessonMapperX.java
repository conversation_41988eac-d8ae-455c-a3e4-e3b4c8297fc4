package com.nnnmkj.thai.module.course.dal.mysql.lesson;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonPageReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LessonMapperX extends BaseMapperX<LessonDTO> {

    default PageResult<LessonDTO> selectPage(LessonPageReqVO reqVO) {
        MPJLambdaWrapper<LessonDTO> wrapper = buildBaseWrapper(reqVO);
        return selectPage(reqVO, wrapper);
    }

    default PageResult<LessonDTO> selectPageByCreator(Long userId, Integer userType, LessonPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(userId, userType);
        MPJLambdaWrapper<LessonDTO> wrapper = buildBaseWrapper(reqVO)
                .likeIfExists(LessonDO::getCreator, creatorQuery);
        return selectPage(reqVO, wrapper);
    }

    @SuppressWarnings("all")
    private MPJLambdaWrapper<LessonDTO> buildBaseWrapper(LessonPageReqVO reqVO) {
        return new MPJLambdaWrapperX<LessonDTO>()
                .selectAll(LessonDO.class)
                .selectAs("t1.nickname", LessonDTO::getNickname)
                .likeIfPresent(LessonDO::getTitle, reqVO.getTitle())
                .likeIfPresent(LessonDO::getDescription, reqVO.getDescription())
                .likeIfPresent(LessonDO::getCoverImage, reqVO.getCoverImage())
                .betweenIfPresent(LessonDO::getCreateTime, reqVO.getCreateTime())
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)")
                .orderByDesc(LessonDO::getId);
    }
}