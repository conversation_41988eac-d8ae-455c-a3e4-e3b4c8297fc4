package com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.nnnmkj.thai.framework.excel.core.annotations.DictFormat;
import com.nnnmkj.thai.framework.excel.core.convert.DictConvert;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionOptionDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 课程作业题目 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AssignmentQuestionRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "11231")
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27036")
    @ExcelProperty("课程ID")
    private Long courseId;

    @Schema(description = "作业ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12524")
    @ExcelProperty("作业ID")
    private Long assignmentId;

    @Schema(description = "题目类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "题目类型", converter = DictConvert.class)
    @DictFormat("assignment_question_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer questionType;

    @Schema(description = "题干", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("题干")
    private String questionStem;

    @Schema(description = "参考答案", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("参考答案")
    private String answer;

    @Schema(description = "题目出处", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("题目出处")
    private String source;

    @Schema(description = "试题解析", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("试题解析")
    private String analysis;

    @Schema(description = "难易度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "难易度", converter = DictConvert.class)
    @DictFormat("assignment_question_difficulty") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer difficulty;

    @Schema(description = "分数（仅自定义类型时使用）")
    @ExcelProperty("分数")
    private Double score;

    @Schema(description = "题目选项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<AssignmentQuestionOptionDO> optionList;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}