package com.nnnmkj.thai.module.learning.controller.admin.wordcard;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.wordcard.vo.WordCardPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordcard.vo.WordCardRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordcard.vo.WordCardSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcarddefinition.WordCardDefinitionDO;
import com.nnnmkj.thai.module.learning.service.wordcard.WordCardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 单词卡")
@RestController
@RequestMapping("/learning/word-card")
@Validated
public class WordCardController {

    @Resource
    private WordCardService wordCardService;

    @PostMapping("/create")
    @Operation(summary = "创建单词卡")
    @PreAuthorize("@ss.hasPermission('learning:word-card:create')")
    public CommonResult<Long> createWordCard(@Valid @RequestBody WordCardSaveReqVO createReqVO) {
        return success(wordCardService.createWordCard(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新单词卡")
    @PreAuthorize("@ss.hasPermission('learning:word-card:update')")
    public CommonResult<Boolean> updateWordCard(@Valid @RequestBody WordCardSaveReqVO updateReqVO) {
        wordCardService.updateWordCard(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除单词卡")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:word-card:delete')")
    public CommonResult<Boolean> deleteWordCard(@RequestParam("id") Long id) {
        wordCardService.deleteWordCard(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得单词卡")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:word-card:query')")
    public CommonResult<WordCardRespVO> getWordCard(@RequestParam("id") Long id) {
        WordCardDO wordCard = wordCardService.getWordCard(id);
        return success(BeanUtils.toBean(wordCard, WordCardRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得单词卡分页")
    @PreAuthorize("@ss.hasPermission('learning:word-card:query')")
    public CommonResult<PageResult<WordCardRespVO>> getWordCardPage(@Valid WordCardPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<WordCardDO> pageResult = wordCardService.getWordCardPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WordCardRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出单词卡 Excel")
    @PreAuthorize("@ss.hasPermission('learning:word-card:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWordCardExcel(@Valid WordCardPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        List<WordCardDO> list = wordCardService.getWordCardPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "单词卡.xls", "数据", WordCardRespVO.class,
                        BeanUtils.toBean(list, WordCardRespVO.class));
    }

    // ==================== 子表（单词卡定义） ====================

    @GetMapping("/word-card-definition/list-by-word-card-id")
    @Operation(summary = "获得单词卡定义列表")
    @Parameter(name = "wordCardId", description = "单词卡编号")
    @PreAuthorize("@ss.hasPermission('learning:word-card:query')")
    public CommonResult<List<WordCardDefinitionDO>> getWordCardDefinitionListByWordCardId(@RequestParam("wordCardId") Long wordCardId) {
        return success(wordCardService.getWordCardDefinitionListByWordCardId(wordCardId));
    }

}