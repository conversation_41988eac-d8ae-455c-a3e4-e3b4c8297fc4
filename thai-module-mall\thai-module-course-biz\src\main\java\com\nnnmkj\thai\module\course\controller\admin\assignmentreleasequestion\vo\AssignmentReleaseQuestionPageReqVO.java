package com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课程作业发布题目分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AssignmentReleaseQuestionPageReqVO extends PageParam {

    @Schema(description = "课程ID", example = "32557")
    private Long courseId;

    @Schema(description = "发布ID", example = "27852")
    private Long assignmentReleaseId;

    @Schema(description = "题目类型", example = "2")
    private Integer questionType;

    @Schema(description = "题干")
    private String questionStem;

    @Schema(description = "参考答案")
    private String answer;

    @Schema(description = "题目出处")
    private String source;

    @Schema(description = "试题解析")
    private String analysis;

    @Schema(description = "难易度")
    private Integer difficulty;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}