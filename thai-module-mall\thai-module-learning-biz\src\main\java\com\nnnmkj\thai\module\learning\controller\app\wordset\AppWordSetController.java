package com.nnnmkj.thai.module.learning.controller.app.wordset;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.learning.controller.admin.membergroupwordset.vo.MemberGroupWordSetSaveReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.*;
import com.nnnmkj.thai.module.learning.convert.wordset.WordSetConvert;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.enums.WordSetVisibilityEnum;
import com.nnnmkj.thai.module.learning.service.membergroupwordset.MemberGroupWordSetService;
import com.nnnmkj.thai.module.learning.service.wordset.AppWordSetService;
import com.nnnmkj.thai.module.learning.service.wordsetcollection.AppWordSetCollectionService;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.*;

@Tag(name = "用户 APP - 学习集")
@RestController
@RequestMapping("/learning/word-set")
@Validated
public class AppWordSetController {

    @Resource
    private AppWordSetService wordSetService;

    @Resource
    private AppWordSetCollectionService wordSetCollectionService;

    @Resource
    private MemberGroupWordSetService memberGroupWordSetService;

    @Resource
    private MemberUserApi memberUserApi;

    @PostMapping("/create")
    @Operation(summary = "创建学习集")
    public CommonResult<Long> createWordSet(@Valid @RequestBody AppWordSetSaveReqVO createReqVO) {
        createReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        return success(wordSetService.createWordSet(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新学习集")
    public CommonResult<Boolean> updateWordSet(@Valid @RequestBody AppWordSetSaveReqVO updateReqVO) {
        updateReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        wordSetService.updateWordSet(updateReqVO);
        return success(true);
    }

    @PutMapping("/update/visibility")
    @Operation(summary = "设置学习集可见范围")
    @Parameter(name = "id", description = "编号", required = true)
    @Parameter(name = "visibility", description = "学习集可见范围", required = true)
    public CommonResult<Boolean> updateWordSetStatus(@Valid @RequestBody AppWordSetVisibilityReqVO reqVO) {
        if (!Arrays.asList(WordSetVisibilityEnum.ARRAYS).contains(reqVO.getVisibility())) {
            throw exception(VISIBILITY_NOT_CORRECT);
        }
        reqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        wordSetService.updateWordSetVisibility(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除学习集")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteWordSet(@RequestParam("id") Long id) {
        wordSetService.deleteWordSet(id, SecurityFrameworkUtils.getLoginUserId());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得学习集")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppWordSetRespVO> getWordSet(@RequestParam("id") Long id) {
        // 查询学习集
        AppWordSetRespVO setRespVO = wordSetService.getWordSetWithInfo(id);
        return success(setRespVO);
    }

    @GetMapping("/top/get")
    @Operation(summary = "获得置顶学习集")
    public CommonResult<AppWordSetTopRespVO> getTopWordSet() {
        return success(wordSetService.getTopWordSet(SecurityFrameworkUtils.getLoginUserId()));
    }

    @GetMapping("/top/set")
    @Operation(summary = "设置置顶学习集")
    @Parameter(name = "setId", description = "学习集编号", required = true, example = "1024")
    public CommonResult<AppWordSetTopRespVO> setTopWordSet(@RequestParam("setId") Long setId) {
        wordSetService.setTopWordSet(setId, SecurityFrameworkUtils.getLoginUserId());
        return success();
    }

    @GetMapping("/class/share")
    @Operation(summary = "学习集分享到班级")
    @Parameters({
            @Parameter(name = "setId", description = "学习集编号", required = true, example = "14"),
            @Parameter(name = "classId", description = "班级编号", required = true, example = "10")
    })
    public CommonResult<Long> shareClassWordSet(@RequestParam("setId") Long setId, @RequestParam("classId") Long classId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        WordSetDO wordSet = wordSetService.getWordSet(setId);
        if (wordSet == null) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
        List<Long> groupIds = memberUserApi.getUserGroups(userId);
        if (CollUtil.isEmpty(groupIds)) {
            throw exception(USER_NOT_IN_CLASS);
        }
        if (!groupIds.contains(classId)) {
            throw exception(USER_NOT_IN_CURRENT_CLASS_GROUP);
        }
        MemberGroupWordSetSaveReqVO saveReqVO = new MemberGroupWordSetSaveReqVO();
        saveReqVO.setWordSetId(wordSet.getId());
        saveReqVO.setGroupId(classId);
        Long id = memberGroupWordSetService.createMemberGroupWordSet(saveReqVO);
        return success(id);
    }

    @PostMapping("/class/batch-share")
    @Operation(summary = "批量分享学习集到班级")
    public CommonResult<Integer> batchShareClassWordSet(@Valid @RequestBody AppWordSetBatchShareReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 验证用户是否在班级中
        List<Long> groupIds = memberUserApi.getUserGroups(userId);
        if (CollUtil.isEmpty(groupIds)) {
            throw exception(USER_NOT_IN_CLASS);
        }
        if (!groupIds.contains(reqVO.getClassId())) {
            throw exception(USER_NOT_IN_CURRENT_CLASS_GROUP);
        }
        
        // 验证学习集是否都存在
        List<WordSetDO> wordSets = wordSetService.getWordSetList(reqVO.getSetIds());
        if (wordSets.size() != reqVO.getSetIds().size()) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
        
        // 批量创建班级-学习集关联
        int count = memberGroupWordSetService.batchCreateMemberGroupWordSet(reqVO.getSetIds(), reqVO.getClassId());
        return success(count);
    }

    @PostMapping("/class/batch-delete")
    @Operation(summary = "批量删除班级中的学习集")
    public CommonResult<Integer> batchDeleteClassWordSet(@Valid @RequestBody AppWordSetBatchDeleteReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 验证用户是否在班级中
        List<Long> groupIds = memberUserApi.getUserGroups(userId);
        if (CollUtil.isEmpty(groupIds)) {
            throw exception(USER_NOT_IN_CLASS);
        }
        if (!groupIds.contains(reqVO.getClassId())) {
            throw exception(USER_NOT_IN_CURRENT_CLASS_GROUP);
        }
        
        // 批量删除班级-学习集关联
        int count = memberGroupWordSetService.batchDeleteMemberGroupWordSet(reqVO.getSetIds(), reqVO.getClassId());
        return success(count);
    }

    @GetMapping("/word/reset")
    @Operation(summary = "重置词书")
    @Parameter(name = "setId", description = "学习集编号", required = true, example = "1024")
    public CommonResult<AppWordSetTopRespVO> resetWord(@RequestParam("setId") Long setId) {
        return success(wordSetService.resetWord(setId, SecurityFrameworkUtils.getLoginUserId()));
    }

    @GetMapping("/page")
    @Operation(summary = "获得学习集分页")
    public CommonResult<PageResult<AppWordSetRespVO>> getWordSetPage(@Valid AppWordSetPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        }
        PageResult<WordSetDTO> pageResult = wordSetService.getWordSetPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        Set<Long> setIds = pageResult.getList().stream().map(WordSetDO::getId).collect(Collectors.toSet());
        // 处理是否收藏返回值
        List<WordSetCollectionDO> wordSetCollectionList = wordSetCollectionService
                .getWordSetCollectionList(setIds, SecurityFrameworkUtils.getLoginUserId(), pageReqVO.getUserType());
        Set<Long> storeSetIds = wordSetCollectionList.stream().map(WordSetCollectionDO::getSetId).collect(Collectors.toSet());
        PageResult<AppWordSetRespVO> result = WordSetConvert.INSTANCE.convertAppPage(pageResult, storeSetIds);
        return success(result);
    }

    @GetMapping("/page/class")
    @Operation(summary = "获得当前班级下的学习集分页")
    public CommonResult<PageResult<AppWordSetRespVO>> getClassWordSetPage(@Valid AppWordSetClassPageReqVO pageReqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        pageReqVO.setUserId(userId);
        pageReqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        PageResult<WordSetDTO> pageResult = wordSetService.getClassWordSetPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        Set<Long> setIds = pageResult.getList().stream().map(WordSetDTO::getId).collect(Collectors.toSet());
        // 处理是否收藏返回值
        List<WordSetCollectionDO> wordSetCollectionList = wordSetCollectionService
                .getWordSetCollectionList(setIds, userId, pageReqVO.getUserType());
        Set<Long> storeSetIds = wordSetCollectionList.stream().map(WordSetCollectionDO::getSetId).collect(Collectors.toSet());
        PageResult<AppWordSetRespVO> result = WordSetConvert.INSTANCE.convertAppPage(pageResult, storeSetIds);
        return success(result);
    }

    @GetMapping("/page/favourite")
    @Operation(summary = "获得我收藏的学习集分页")
    public CommonResult<PageResult<AppWordSetRespVO>> getFavoriteWordSetPage(@Valid AppWordSetFavoritePageReqVO pageReqVO) {
        pageReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        pageReqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        PageResult<WordSetDTO> pageResult = wordSetService.getFavoriteWordSetPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        Set<Long> setIds = pageResult.getList().stream().map(WordSetDTO::getId).collect(Collectors.toSet());
        // 处理是否收藏返回值
        List<WordSetCollectionDO> wordSetCollectionList = wordSetCollectionService
                .getWordSetCollectionList(setIds, SecurityFrameworkUtils.getLoginUserId(), pageReqVO.getUserType());
        Set<Long> storeSetIds = wordSetCollectionList.stream().map(WordSetCollectionDO::getSetId).collect(Collectors.toSet());
        PageResult<AppWordSetRespVO> result = WordSetConvert.INSTANCE.convertAppPage(pageResult, storeSetIds);
        return success(result);
    }

    @GetMapping("/page/created")
    @Operation(summary = "获得我创建的学习集分页")
    public CommonResult<PageResult<AppWordSetRespVO>> getCreatedWordSetPage(@Valid AppWordSetFavoritePageReqVO pageReqVO) {
        pageReqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        pageReqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        AppWordSetPageReqVO appWordSetPageReqVO = BeanUtils.toBean(pageReqVO, AppWordSetPageReqVO.class);
        PageResult<WordSetDTO> pageResult = wordSetService.getWordSetPage(appWordSetPageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        Set<Long> setIds = pageResult.getList().stream().map(WordSetDO::getId).collect(Collectors.toSet());
        // 处理是否收藏返回值
        List<WordSetCollectionDO> wordSetCollectionList = wordSetCollectionService
                .getWordSetCollectionList(setIds, pageReqVO.getUserId(), pageReqVO.getUserType());
        Set<Long> storeSetIds = wordSetCollectionList.stream().map(WordSetCollectionDO::getSetId).collect(Collectors.toSet());
        PageResult<AppWordSetRespVO> result = WordSetConvert.INSTANCE.convertAppPage(pageResult, storeSetIds);
        return success(result);
    }

    // ==================== 子表（单词卡） ====================
    @GetMapping("/word-card/list-by-word-set-id")
    @Operation(summary = "获得单词卡列表")
    @Parameter(name = "wordSetId", description = "学习集编号")
    public CommonResult<List<WordCardDO>> getWordCardListByWordSetId(@RequestParam("wordSetId") Long wordSetId) {
        return success(wordSetService.getWordCardListByWordSetId(wordSetId));
    }

}