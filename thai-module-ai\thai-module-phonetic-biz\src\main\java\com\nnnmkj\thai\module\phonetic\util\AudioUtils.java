package com.nnnmkj.thai.module.phonetic.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.*;

/**
 * 音频工具类
 */
@Slf4j
public class AudioUtils {

    /**
     * PCM转WAV
     * 这个方法将PCM文件转换为WAV文件，添加WAV头信息
     *
     * @param pcmData PCM原始数据
     * @param sampleRate 采样率，通常为16000Hz或8000Hz
     * @param bitsPerSample 位深度，通常为16位
     * @param channels 声道数，通常为1(单声道)
     * @return WAV文件的字节数组
     */
    public static byte[] pcmToWav(byte[] pcmData, int sampleRate, short bitsPerSample, short channels) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 写入WAV文件头
            writeWavHeader(outputStream, pcmData.length, sampleRate, bitsPerSample, channels);
            
            // 写入PCM数据
            outputStream.write(pcmData);
            
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("PCM转WAV失败", e);
            throw new RuntimeException("PCM转WAV失败", e);
        }
    }
    
    /**
     * PCM文件转WAV文件
     *
     * @param pcmFile PCM文件
     * @param sampleRate 采样率
     * @param bitsPerSample 位深度
     * @param channels 声道数
     * @return WAV文件的字节数组
     */
    public static byte[] pcmFileToWav(File pcmFile, int sampleRate, short bitsPerSample, short channels) {
        try {
            // 读取PCM文件数据
            byte[] pcmData = FileUtil.readBytes(pcmFile);
            
            // 转换为WAV文件
            return pcmToWav(pcmData, sampleRate, bitsPerSample, channels);
        } catch (Exception e) {
            log.error("PCM文件转WAV失败", e);
            throw new RuntimeException("PCM文件转WAV失败", e);
        }
    }
    
    /**
     * 写入WAV文件头信息
     * WAV文件头格式参考：http://soundfile.sapp.org/doc/WaveFormat/
     *
     * @param outputStream 输出流
     * @param pcmDataLength PCM数据长度
     * @param sampleRate 采样率
     * @param bitsPerSample 位深度
     * @param channels 声道数
     * @throws IOException 如果写入出错
     */
    private static void writeWavHeader(OutputStream outputStream, int pcmDataLength, 
                                      int sampleRate, short bitsPerSample, short channels) throws IOException {
        // RIFF header
        // Chunk ID: "RIFF"
        outputStream.write(new byte[]{'R', 'I', 'F', 'F'});
        
        // Chunk Size: 4 + (8 + 16) + (8 + pcmDataLength)
        int fileLength = 36 + pcmDataLength;
        writeLittleEndian(outputStream, fileLength, 4);
        
        // Format: "WAVE"
        outputStream.write(new byte[]{'W', 'A', 'V', 'E'});
        
        // Sub-chunk 1: Format Chunk
        // Subchunk1 ID: "fmt "
        outputStream.write(new byte[]{'f', 'm', 't', ' '});
        
        // Subchunk1 Size: 16 for PCM
        writeLittleEndian(outputStream, 16, 4);
        
        // Audio Format: 1 for PCM
        writeLittleEndian(outputStream, 1, 2);
        
        // Number of Channels
        writeLittleEndian(outputStream, channels, 2);
        
        // Sample Rate
        writeLittleEndian(outputStream, sampleRate, 4);
        
        // Byte Rate: SampleRate * NumChannels * BitsPerSample/8
        int byteRate = sampleRate * channels * bitsPerSample / 8;
        writeLittleEndian(outputStream, byteRate, 4);
        
        // Block Align: NumChannels * BitsPerSample/8
        short blockAlign = (short) (channels * bitsPerSample / 8);
        writeLittleEndian(outputStream, blockAlign, 2);
        
        // Bits Per Sample
        writeLittleEndian(outputStream, bitsPerSample, 2);
        
        // Sub-chunk 2: Data Chunk
        // Subchunk2 ID: "data"
        outputStream.write(new byte[]{'d', 'a', 't', 'a'});
        
        // Subchunk2 Size: NumSamples * NumChannels * BitsPerSample/8
        writeLittleEndian(outputStream, pcmDataLength, 4);
    }
    
    /**
     * 以小端格式写入数字
     *
     * @param outputStream 输出流
     * @param value 要写入的值
     * @param size 字节数
     * @throws IOException 如果写入出错
     */
    private static void writeLittleEndian(OutputStream outputStream, int value, int size) throws IOException {
        for (int i = 0; i < size; i++) {
            outputStream.write((byte) (value & 0xFF));
            value >>= 8;
        }
    }
} 