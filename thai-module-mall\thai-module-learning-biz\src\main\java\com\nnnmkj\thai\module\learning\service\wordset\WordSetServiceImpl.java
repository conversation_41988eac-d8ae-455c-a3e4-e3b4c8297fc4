package com.nnnmkj.thai.module.learning.service.wordset;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.question.QuestionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.questionoption.QuestionOptionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDTO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcarddefinition.WordCardDefinitionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetPageDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.question.QuestionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.questionoption.QuestionOptionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordcard.WordCardMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordcarddefinition.WordCardDefinitionMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.wordset.WordSetMapperPage;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.wordsetcollection.WordSetCollectionMapper;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollectionstatistic.WordSetCollectionStatisticDO;
import com.nnnmkj.thai.module.learning.service.wordsetcollectionstatistic.WordSetCollectionStatisticService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.*;

/**
 * 学习集 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WordSetServiceImpl implements WordSetService {

    @Resource
    private WordSetMapper wordSetMapper;
    @Resource
    private WordSetMapperPage wordSetMapperPage;
    @Resource
    private WordCardMapper wordCardMapper;
    @Resource
    private WordCardDefinitionMapper wordCardDefinitionMapper;
    @Resource
    private QuestionMapper questionMapper;
    @Resource
    private QuestionOptionMapper questionOptionMapper;
    @Resource
    private QuestionGeneratorService questionGeneratorService;

    @Resource
    private MemberUserApi memberUserApi;

    @Resource
    private WordSetCollectionMapper wordSetCollectionMapper;
    
    @Resource
    private WordSetCollectionStatisticService wordSetCollectionStatisticService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createWordSet(WordSetSaveReqVO createReqVO) {
        // 插入
        WordSetDO wordSet = BeanUtils.toBean(createReqVO, WordSetDO.class);
        wordSetMapper.insert(wordSet);
        Long wordSetId = wordSet.getId();

        List<WordCardDTO> wordCards = createReqVO.getWordCards();
        // 插入子表
        createWordCardList(wordSet.getId(), wordCards);
        // 单词卡为空，直接返回
        if (CollUtil.isEmpty(wordCards)) {
            return wordSetId;
        }
        // 根据单词卡生成题目、题目选项
        List<WordCardDefinitionDO> wordCardDefinitionList = wordCards.stream().map(WordCardDTO::getDefinitions)
                .filter(CollUtil::isNotEmpty).flatMap(Collection::stream).toList();
        Set<String> definitions = wordCardDefinitionList.stream().map(WordCardDefinitionDO::getDefinition).collect(Collectors.toSet());
        questionGeneratorService.generateQuestions(wordSetId, wordCards, definitions);
        // 返回
        return wordSetId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWordSet(WordSetSaveReqVO reqVO) {
        Long wordSetId = reqVO.getId();
        // 传入的单词卡列表
        List<WordCardDTO> wordCards = reqVO.getWordCards();
        // 查询已存在的单词卡
        List<WordCardDO> existWordCards = wordCardMapper.selectList(new LambdaQueryWrapper<WordCardDO>()
                .eq(WordCardDO::getWordSetId, wordSetId));
        // 1. 处理学习集
        WordSetDO wordSetDO = handleWordSet(reqVO);
        // 2. 处理单词卡
        List<WordCardDO> savedWordCards = handleWordCards(wordCards, wordSetDO.getId());
        // 3. 处理单词卡定义
        handleWordCardDefinitions(wordCards, savedWordCards, wordSetDO.getId());
        // 新增的单词卡为空，直接返回
        if (CollUtil.isEmpty(wordCards)) {
            return;
        }
        // 根据单词卡生成题目、题目选项
        List<WordCardDTO> wordCardDTOS = filterExistingQuestions(existWordCards, wordCards);
        // 查询已存在的单词定义
        List<Long> wordCardIds = existWordCards.stream().map(WordCardDO::getId).toList();
        Set<String> definitions;
        if (CollUtil.isNotEmpty(wordCardIds)) {
            LambdaQueryWrapper<WordCardDefinitionDO> queryWrapper = new LambdaQueryWrapper<WordCardDefinitionDO>()
                    .in(WordCardDefinitionDO::getWordCardId, wordCardIds);
            List<WordCardDefinitionDO> wordCardDefinitionDOS = wordCardDefinitionMapper.selectList(queryWrapper);
            // 提取定义列表
            definitions = wordCardDefinitionDOS.stream().map(WordCardDefinitionDO::getDefinition)
                    .collect(Collectors.toSet());
            // 提取传入的单词定义
            Set<String> reqDefinitions = wordCards.stream().map(WordCardDTO::getDefinitions)
                    .filter(CollUtil::isNotEmpty).flatMap(Collection::stream)
                    .map(WordCardDefinitionDO::getDefinition).collect(Collectors.toSet());
            // 合并
            definitions.addAll(reqDefinitions);

        } else {
            // 提取传入的单词定义
            definitions = wordCards.stream().map(WordCardDTO::getDefinitions)
                    .filter(CollUtil::isNotEmpty).flatMap(Collection::stream)
                    .map(WordCardDefinitionDO::getDefinition).collect(Collectors.toSet());
        }

        questionGeneratorService.generateQuestions(wordSetId, wordCardDTOS, definitions);
    }

    /**
     * 过滤掉已存在的单词卡
     * @param existWordCards 已存在的单词卡列表
     * @param wordCards 传入的单词卡列表
     * @return 过滤后的单词卡列表
     */
    private List<WordCardDTO> filterExistingQuestions(List<WordCardDO> existWordCards, List<WordCardDTO> wordCards) {
        if (CollUtil.isEmpty(wordCards)) {
            return Collections.emptyList();
        }
        // 获取已存在的单词卡ID列表
        List<Long> wordCardIds = existWordCards.stream().map(WordCardDO::getId).collect(Collectors.toList());
        // 获取已存在的单词卡ID及其定义
        if (CollUtil.isEmpty(wordCardIds)) {
            return wordCards;
        }
        List<WordCardDefinitionDO> wordCardDefinitionDOS = wordCardDefinitionMapper.selectList(
                new LambdaQueryWrapper<WordCardDefinitionDO>()
                        .in(WordCardDefinitionDO::getWordCardId, wordCardIds)
        );
        // map形式：单词卡ID为key，定义为value
        Map<Long, Set<String>> existingWordCardDefinitions = wordCardDefinitionDOS.stream()
            .collect(Collectors.groupingBy(
                    WordCardDefinitionDO::getWordCardId,
                    Collectors.mapping(WordCardDefinitionDO::getDefinition, Collectors.toSet())
            ));

        // 过滤掉已存在的单词卡，如果定义相同则保留
        List<WordCardDTO> newWordCards = new ArrayList<>();
        for (WordCardDTO wordCard : wordCards) {
            Long wordCardId = wordCard.getId();
            Set<String> existingDefinitions = existingWordCardDefinitions.getOrDefault(wordCardId, Collections.emptySet());
            List<WordCardDefinitionDO> wordCardDefinitions = wordCard.getDefinitions();
            if (CollUtil.isEmpty(wordCardDefinitions)) {
                continue;
            }
            Set<String> currentDefinitions = wordCardDefinitions.stream()
                    .map(WordCardDefinitionDO::getDefinition)
                    .collect(Collectors.toSet());

            if (!existingDefinitions.equals(currentDefinitions)) {
                // 如果定义不同，删除旧题目
                questionMapper.delete(new LambdaQueryWrapper<QuestionDO>().eq(QuestionDO::getWordCardId, wordCardId));
                newWordCards.add(wordCard);
            }
        }

        return newWordCards;
    }

    private WordSetDO handleWordSet(WordSetSaveReqVO reqVO) {
        WordSetDO wordSetDO;
        if (reqVO.getId() != null) {
            // 更新现有学习集
            wordSetDO = wordSetMapper.selectById(reqVO.getId());
            if (wordSetDO == null) {
                throw exception(WORD_SET_NOT_EXISTS);
            }
            wordSetDO = BeanUtils.toBean(reqVO, WordSetDO.class);
            wordSetMapper.updateById(wordSetDO);
        } else {
            // 创建新学习集
            wordSetDO = BeanUtils.toBean(reqVO, WordSetDO.class);
            wordSetMapper.insert(wordSetDO);
        }
        return wordSetDO;
    }

    private List<WordCardDO> handleWordCards(List<WordCardDTO> wordCards, Long wordSetId) {
        if (CollUtil.isEmpty(wordCards)) {
            return Collections.emptyList();
        }
        List<WordCardDO> savedWordCards = new ArrayList<>();

        // 获取该学习集下现有的所有单词卡
        Map<String, WordCardDO> existingWordCards = wordCardMapper.selectList(
                        new LambdaQueryWrapper<WordCardDO>()
                                .eq(WordCardDO::getWordSetId, wordSetId))
                .stream()
                .collect(Collectors.toMap(WordCardDO::getWord, Function.identity(), (a, b) -> a));

        // 获取传入的单词卡的单词集合
        Set<String> inputWords = wordCards.stream().map(WordCardDTO::getWord).collect(Collectors.toSet());

        // 找出需要删除的单词卡
        List<WordCardDO> wordCardsToDelete = existingWordCards.values().stream()
                .filter(wordCard -> !inputWords.contains(wordCard.getWord())).toList();

        // 删除需要删除的单词卡及其相关定义
        for (WordCardDO wordCardToDelete : wordCardsToDelete) {
            Long wordCardId = wordCardToDelete.getId();
            // 删除单词卡定义
            wordCardDefinitionMapper.delete(new LambdaQueryWrapper<WordCardDefinitionDO>().eq(WordCardDefinitionDO::getWordCardId, wordCardId));
            // 删除单词卡
            wordCardMapper.deleteById(wordCardId);
        }

        for (WordCardDTO cardDTO : wordCards) {
            WordCardDO wordCardDO;
            if (cardDTO.getId() != null) {
                // 有ID的单词卡 - 更新
                wordCardDO = wordCardMapper.selectById(cardDTO.getId());
                if (wordCardDO == null) {
                    throw exception(WORD_CARD_NOT_EXISTS);
                }
                wordCardDO.setWord(cardDTO.getWord());
                wordCardDO.setPhoneticSymbol(cardDTO.getPhoneticSymbol());
                wordCardDO.setImageUrl(cardDTO.getImageUrl());
                wordCardDO.setAudioUrl(cardDTO.getAudioUrl());
                wordCardMapper.updateById(wordCardDO);
            } else if (existingWordCards.containsKey(cardDTO.getWord())) {
                // 无ID但有相同词语的单词卡 - 更新
                wordCardDO = existingWordCards.get(cardDTO.getWord());
                wordCardDO.setPhoneticSymbol(cardDTO.getPhoneticSymbol());
                wordCardDO.setImageUrl(cardDTO.getImageUrl());
                wordCardDO.setAudioUrl(cardDTO.getAudioUrl());
                wordCardMapper.updateById(wordCardDO);
                // 更新DTO的ID、用于后续操作
                cardDTO.setId(wordCardDO.getId());
            } else {
                // 全新单词卡 - 插入
                wordCardDO = new WordCardDO();
                wordCardDO.setWordSetId(wordSetId);
                wordCardDO.setWord(cardDTO.getWord());
                wordCardDO.setPhoneticSymbol(cardDTO.getPhoneticSymbol());
                wordCardDO.setImageUrl(cardDTO.getImageUrl());
                wordCardDO.setAudioUrl(cardDTO.getAudioUrl());
                wordCardMapper.insert(wordCardDO);
                // 更新DTO的ID、用于后续操作
                cardDTO.setId(wordCardDO.getId());
            }
            savedWordCards.add(wordCardDO);
        }

        return savedWordCards;
    }

    private void handleWordCardDefinitions(List<WordCardDTO> wordCards, List<WordCardDO> savedWordCards, Long wordSetId) {
        if (CollUtil.isEmpty(wordCards)) {
            return;
        }

        // 建立词语到单词卡ID的映射
        Map<String, Long> wordToCardIdMap = savedWordCards.stream()
                .collect(Collectors.toMap(WordCardDO::getWord, WordCardDO::getId));

        for (WordCardDTO cardDTO : wordCards) {
            if (CollUtil.isEmpty(cardDTO.getDefinitions())) {
                continue;
            }

            Long wordCardId = wordToCardIdMap.get(cardDTO.getWord());
            if (wordCardId == null) {
                continue;
            }

            // 获取该单词卡下现有的所有定义
            List<WordCardDefinitionDO> existingDefinitions = wordCardDefinitionMapper.selectList(
                    new LambdaQueryWrapper<WordCardDefinitionDO>()
                            .eq(WordCardDefinitionDO::getWordCardId, wordCardId));

            for (WordCardDefinitionDO definitionDTO : cardDTO.getDefinitions()) {
                WordCardDefinitionDO definitionDO;
                if (definitionDTO.getId() != null) {
                    // 有ID的定义 - 更新
                    definitionDO = wordCardDefinitionMapper.selectById(definitionDTO.getId());
                    if (definitionDO == null) {
                        throw exception(WORD_CARD_DEFINITION_NOT_EXISTS);
                    }
                    definitionDO.setDefinition(definitionDTO.getDefinition());
                    wordCardDefinitionMapper.updateById(definitionDO);
                } else {
                    // 检查是否有相同内容的定义
                    Optional<WordCardDefinitionDO> existingDef = existingDefinitions.stream()
                            .filter(d -> d.getDefinition().equals(definitionDTO.getDefinition()))
                            .findFirst();

                    if (existingDef.isPresent()) {
                        // 有相同内容的定义 - 更新
                        definitionDO = existingDef.get();
                        definitionDO.setDefinition(definitionDTO.getDefinition());
                        wordCardDefinitionMapper.updateById(definitionDO);
                    } else {
                        // 全新定义 - 插入
                        definitionDO = new WordCardDefinitionDO();
                        definitionDO.setWordSetId(wordSetId);
                        definitionDO.setWordCardId(wordCardId);
                        definitionDO.setDefinition(definitionDTO.getDefinition());
                        wordCardDefinitionMapper.insert(definitionDO);
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWordSet(Long id) {
        // 校验存在
        validateWordSetExists(id);
        // 删除
        wordSetMapper.deleteById(id);

        // 删除学习集收藏
        wordSetCollectionMapper.delete(new LambdaQueryWrapper<WordSetCollectionDO>()
                .eq(WordSetCollectionDO::getSetId, id));
        
        // 删除学习集收藏统计
        WordSetCollectionStatisticDO statistic = wordSetCollectionStatisticService.getWordSetCollectionStatisticBySetId(id);
        if (statistic != null) {
            wordSetCollectionStatisticService.deleteWordSetCollectionStatistic(statistic.getId());
        }
                
        // 删除子表
        deleteWordCardByWordSetId(id);
    }

    private void validateWordSetExists(Long id) {
        if (wordSetMapper.selectById(id) == null) {
            throw exception(WORD_SET_NOT_EXISTS);
        }
    }

    @Override
    public WordSetDO getWordSet(Long id) {
        return wordSetMapper.selectById(id);
    }

    @Override
    public WordSetRespVO getWordSetWithCardsAndDefinitions(Long id) {
        // 查询学习集基本信息
        WordSetDO wordSetDO = wordSetMapper.selectById(id);
        if (wordSetDO == null) {
            return null;
        }
        // 查询用户昵称
        MemberUserRespDTO user = memberUserApi.getUser(wordSetDO.getUserId());
        // 构建响应对象
        WordSetRespVO respVO = BeanUtils.toBean(wordSetDO, WordSetRespVO.class);
        if (user != null) {
            respVO.setNickname(user.getNickname());
        }
        // 查询单词卡列表
        List<WordCardDO> wordCardDOs = wordCardMapper.selectList(
                new LambdaQueryWrapper<WordCardDO>()
                        .eq(WordCardDO::getWordSetId, id));
        // 转换为DTO并填充定义
        List<WordCardDTO> wordCardDTOs = wordCardDOs.stream().map(card -> {
            WordCardDTO dto = BeanUtils.toBean(card, WordCardDTO.class);
            // 查询单词卡定义
            List<WordCardDefinitionDO> definitions = wordCardDefinitionMapper.selectList(
                    new LambdaQueryWrapper<WordCardDefinitionDO>()
                            .eq(WordCardDefinitionDO::getWordCardId, card.getId()));
            dto.setDefinitions(definitions);

            return dto;
        }).collect(Collectors.toList());

        respVO.setWordCards(wordCardDTOs);

        return respVO;
    }

    @Override
    public Long getWordSetCount() {
        // 查询总记录数
        return wordSetMapper.selectCount();
    }

    @Override
    public PageResult<WordSetPageDTO> getWordSetPage(WordSetPageReqVO pageReqVO) {
        return wordSetMapperPage.selectPage(pageReqVO);
    }

    @Override
    public List<WordSetDO> getWordSetList(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return wordSetMapper.selectList(new LambdaQueryWrapperX<WordSetDO>().in(WordSetDO::getId, ids));
    }

    @Override
    public List<WordSetDO> getWordSetList(WordSetPageReqVO reqVO) {
        return wordSetMapper.selectList(reqVO);
    }

    @Override
    public List<WordCardDO> getWordCardListByWordSetId(Long wordSetId) {
        return wordCardMapper.selectListByWordSetId(wordSetId);
    }

    private void createWordCardList(Long wordSetId, List<WordCardDTO> list) {
        // 列表为空，直接返回
        if (CollUtil.isEmpty(list)) {
            return;
        }
        // 单词卡词义列表
        List<WordCardDefinitionDO> wordCardDefinitionDOS = new ArrayList<>();
        // 循环获取单词卡内容
        for (WordCardDTO wordCardDTO : list) {
            // 单词卡
            WordCardDO wordCardDO = BeanUtils.toBean(wordCardDTO, WordCardDO.class);
            wordCardDO.setWordSetId(wordSetId);
            wordCardMapper.insert(wordCardDO);
            // 插入后的单词卡ID，设置到单词卡对象中
            wordCardDTO.setId(wordCardDO.getId());
            // 单词卡词义
            List<WordCardDefinitionDO> wordCardDefinitions = wordCardDTO.getDefinitions();
            // 如果列表为空，直接返回
            if (CollUtil.isEmpty(wordCardDefinitions)) {
                continue;
            }
            wordCardDefinitions.forEach(o -> o.setWordSetId(wordSetId).setWordCardId(wordCardDO.getId()));
            wordCardDefinitionDOS.addAll(wordCardDefinitions);
        }
        wordCardDefinitionMapper.insertBatch(wordCardDefinitionDOS);
    }

    private void deleteWordCardByWordSetId(Long wordSetId) {
        // 删除单词卡
        wordCardMapper.deleteByWordSetId(wordSetId);
        // 删除单词卡词义
        wordCardDefinitionMapper.delete(new LambdaQueryWrapperX<WordCardDefinitionDO>()
                .eq(WordCardDefinitionDO::getWordSetId, wordSetId));
        // 删除题目
        List<QuestionDO> questionDOS = questionMapper.selectList(new LambdaQueryWrapperX<QuestionDO>()
                .eq(QuestionDO::getSetId, wordSetId));
        List<Long> questionIds = questionDOS.stream().map(QuestionDO::getId).toList();
        if (CollUtil.isNotEmpty(questionIds)) {
            questionMapper.deleteByIds(questionIds);
            // 删除题目选项
            questionOptionMapper.delete(new LambdaQueryWrapperX<QuestionOptionDO>()
                    .in(QuestionOptionDO::getQuestionId, questionIds));
        }
    }
}