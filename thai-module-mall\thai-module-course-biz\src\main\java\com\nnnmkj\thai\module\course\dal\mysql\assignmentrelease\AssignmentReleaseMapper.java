package com.nnnmkj.thai.module.course.dal.mysql.assignmentrelease;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.AssignmentReleasePageReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentrelease.vo.AppAssignmentReleasePageReqVO;
import com.nnnmkj.thai.module.course.controller.app.assignmentrelease.vo.AppAssignmentReleaseRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentrelease.AssignmentReleaseDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 课程作业发布 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssignmentReleaseMapper extends BaseMapperX<AssignmentReleaseDO> {

    default PageResult<AssignmentReleaseDO> selectPage(AssignmentReleasePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssignmentReleaseDO>()
                .likeIfPresent(AssignmentReleaseDO::getTitle, reqVO.getTitle())
                .eqIfPresent(AssignmentReleaseDO::getCourseId, reqVO.getCourseId())
                .orderByDesc(AssignmentReleaseDO::getId));
    }

    default PageResult<AppAssignmentReleaseRespVO> selectPage(AppAssignmentReleasePageReqVO reqVO) {
        MPJLambdaWrapper<AssignmentReleaseDO> wrapper = new MPJLambdaWrapperX<AssignmentReleaseDO>()
                .select(AssignmentReleaseDO::getId, AssignmentReleaseDO::getTitle, AssignmentReleaseDO::getStartTime, AssignmentReleaseDO::getEndTime)
                .likeIfExists(AssignmentReleaseDO::getTitle, reqVO.getTitle())
                .eqIfExists(AssignmentReleaseDO::getCourseId, reqVO.getCourseId())
                .orderByDesc(AssignmentReleaseDO::getId)
                .leftJoin(LessonDO.class, LessonDO::getId, AssignmentReleaseDO::getCourseId)
                .selectAs(LessonDO::getTitle, AppAssignmentReleaseRespVO::getCourseName);
        return selectJoinPage(reqVO, AppAssignmentReleaseRespVO.class, wrapper);
    }
}