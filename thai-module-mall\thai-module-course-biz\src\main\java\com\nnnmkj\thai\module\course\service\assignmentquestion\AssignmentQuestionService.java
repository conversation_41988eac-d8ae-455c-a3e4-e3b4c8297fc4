package com.nnnmkj.thai.module.course.service.assignmentquestion;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionCreateFromBankReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.assignmentquestion.vo.AssignmentQuestionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionOptionDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 课程作业题目 Service 接口
 *
 * <AUTHOR>
 */
public interface AssignmentQuestionService {

    /**
     * 从题库中选题并创建题目
     *
     * @param createReqVO 创建信息
     */
    void createQuestionFromBank(@Valid AssignmentQuestionCreateFromBankReqVO createReqVO);

    /**
     * 创建课程作业题目
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAssignmentQuestion(@Valid AssignmentQuestionSaveReqVO createReqVO);

    /**
     * 更新课程作业题目
     *
     * @param updateReqVO 更新信息
     */
    void updateAssignmentQuestion(@Valid AssignmentQuestionSaveReqVO updateReqVO);

    /**
     * 删除课程作业题目
     *
     * @param id 编号
     */
    void deleteAssignmentQuestion(Long id);

    /**
     * 根据作业编号删除课程作业题目
     *
     * @param id 作业编号
     */
    void deleteAssignmentQuestionByAssignmentId(Long id);

    /**
     * 获得课程作业题目
     *
     * @param id 编号
     * @return 课程作业题目
     */
    AssignmentQuestionDO getAssignmentQuestion(Long id);

    /**
     * 根据作业编号获得课程作业题目列表
     *
     * @param id 作业编号
     * @return 课程作业题目列表
     */
    List<AssignmentQuestionDO> getAssignmentQuestionListByAssignmentId(Long id);

    /**
     * 根据作业编号集合获得课程作业题目列表
     *
     * @param ids 作业编号集合
     * @return 课程作业题目列表
     */
    List<AssignmentQuestionDO> getAssignmentQuestionListByAssignmentIds(List<Long> ids);

    /**
     * 获得课程作业题目分页
     *
     * @param pageReqVO 分页查询
     * @return 课程作业题目分页
     */
    PageResult<AssignmentQuestionDO> getAssignmentQuestionPage(AssignmentQuestionPageReqVO pageReqVO);

    /**
     * 根据ID集合获取题目列表
     *
     * @param questionIds 题目ID集合
     * @return 题目列表
     */
    List<AssignmentQuestionDO> getAssignmentQuestionList(List<Long> questionIds);

    // ==================== 子表（课程作业题目选项） ====================

    /**
     * 获得课程作业题目选项列表
     *
     * @param questionId 题目ID
     * @return 课程作业题目选项列表
     */
    List<AssignmentQuestionOptionDO> getAssignmentQuestionOptionListByQuestionId(Long questionId);

    /**
     * 批量获取题目选项，按题目ID分组返回
     *
     * @param questionIds 题目ID集合
     * @return Map<questionId, 选项列表>
     */
    Map<Long, List<AssignmentQuestionOptionDO>> getAssignmentQuestionOptionMapByQuestionIds(List<Long> questionIds);

}