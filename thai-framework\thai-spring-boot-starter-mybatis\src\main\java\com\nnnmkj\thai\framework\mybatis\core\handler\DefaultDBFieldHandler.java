package com.nnnmkj.thai.framework.mybatis.core.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.enums.CommonConstants;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.member.api.user.MemberUserApi;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Lazy;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 通用参数填充实现类
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Lazy
    @Resource
    private MemberUserApi memberUserApi;

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.nonNull(metaObject) && metaObject.getOriginalObject() instanceof BaseDO baseDO) {
            LocalDateTime current = LocalDateTime.now();
            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseDO.getCreateTime())) {
                baseDO.setCreateTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseDO.getUpdateTime())) {
                baseDO.setUpdateTime(current);
            }

            // 用户类型
            Integer userType = WebFrameworkUtils.getLoginUserType();
            // 登录用户ID
            Long userId = WebFrameworkUtils.getLoginUserId();
            // 组合创建人信息
            String creator = null;
            if (Objects.nonNull(userId)) {
                creator = getCreatorOrUpdater(userType, userId);
            }
            // 当前登录用户不为空，creator不为空
            if (StringUtils.isNotEmpty(creator)) {
                baseDO.setCreator(creator);
            }
            // 当前登录用户不为空，creator不为空
            if (StringUtils.isNotEmpty(creator)) {
                baseDO.setUpdater(creator);
            }
        }
    }

    /**
     * 组合创建人或者更新人信息
     * @param userType 用户类型
     * @param userId 登录用户ID
     * @return 组合后的创建人或者更新人信息
     */
    private String getCreatorOrUpdater(Integer userType, Long userId) {
        String creator = null;
        if (Objects.nonNull(userType)) {
            UserTypeEnum typeEnum = UserTypeEnum.valueOf(userType);
            if (Objects.nonNull(typeEnum)) {
                if (typeEnum == UserTypeEnum.MEMBER) {
                    // 获取会员用户格式化字符串
                    String memberUserFormat = CommonConstants.MEMBER_USER_ID_PREFIX + userId + CommonConstants.USER_ID_SUFFIX;
                    // 查询是否存在系统用户
                    Long loginUserSystemId = memberUserApi.getLoginUserSystemId();
                    if (loginUserSystemId != null) {
                        // 获取系统用户格式化字符串
                        String systemUserFormat = CommonConstants.SYSTEM_USER_ID_PREFIX + loginUserSystemId + CommonConstants.USER_ID_SUFFIX;
                        creator = systemUserFormat + memberUserFormat;
                    } else {
                        creator = memberUserFormat;
                    }
                } else if (typeEnum == UserTypeEnum.ADMIN) {
                    // 获取系统用户格式化字符串
                    String systemUserFormat = CommonConstants.SYSTEM_USER_ID_PREFIX + userId + CommonConstants.USER_ID_SUFFIX;
                    // 查询是否存在会员用户
                    Long memberUserId = memberUserApi.getUserIdBySystemUserId(userId);
                    if (memberUserId != null) {
                        // 获取系统用户格式化字符串
                        String memberUserFormat = CommonConstants.MEMBER_USER_ID_PREFIX + memberUserId + CommonConstants.USER_ID_SUFFIX;
                        creator = systemUserFormat + memberUserFormat;
                    } else {
                        creator = systemUserFormat;
                    }
                }
            }
        }
        return creator;
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        Object modifyTime = getFieldValByName("updateTime", metaObject);
        if (Objects.isNull(modifyTime)) {
            setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        }

        // 用户类型
        Integer userType = WebFrameworkUtils.getLoginUserType();
        // 登录用户ID
        Long userId = WebFrameworkUtils.getLoginUserId();
        // 组合更新人信息
        String updater = null;
        if (Objects.nonNull(userId)) {
            updater = getCreatorOrUpdater(userType, userId);
        }
        if (StringUtils.isNotEmpty(updater)) {
            setFieldValByName("updater", updater, metaObject);
        }
    }
}
