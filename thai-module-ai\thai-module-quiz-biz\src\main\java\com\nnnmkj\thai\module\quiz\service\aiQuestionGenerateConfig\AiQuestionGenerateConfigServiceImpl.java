package com.nnnmkj.thai.module.quiz.service.aiQuestionGenerateConfig;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.util.StringUtils;
import com.nnnmkj.thai.framework.ai.core.enums.AiPlatformEnum;
import com.nnnmkj.thai.framework.ai.core.factory.AiModelFactory;
import com.nnnmkj.thai.framework.ai.core.model.fastgpt.api.FastGPTApi;
import com.nnnmkj.thai.framework.ai.core.model.fastgpt.api.vo.FastGPTRequestVO;
import com.nnnmkj.thai.framework.ai.core.util.preprocessJsonUtil;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.json.JsonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.ai.api.aiModel.AiModelApi;
import com.nnnmkj.thai.module.ai.api.aiModel.vo.AiApiKeyVO;
import com.nnnmkj.thai.module.ai.api.aiModel.vo.AiModelVO;
import com.nnnmkj.thai.module.ai.enums.ErrorCodeConstants;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateConfig.vo.AiQuestionGenerateConfigPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateConfig.vo.AiQuestionGenerateConfigSaveReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateConfig.vo.QuestionGenerateDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateConfig.AiQuestionGenerateConfigDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskRecordDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankKnowledgePoint.QuestionBankKnowledgePointDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankOption.QuestionBankOptionDO;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiQuestionGenerateConfig.AiQuestionGenerateConfigMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiQuestionGenerateTask.AiQuestionGenerateTaskMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiQuestionGenerateTask.AiQuestionGenerateTaskRecordMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.aiquestion.AiQuestionMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.questionBank.QuestionBankKnowledgePointMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.questionBank.QuestionBankMapper;
import com.nnnmkj.thai.module.quiz.dal.mysql.questionBank.QuestionBankOptionMapper;
import com.nnnmkj.thai.module.quiz.enums.AiQuestionStatusEnum;
import com.nnnmkj.thai.module.quiz.enums.CreateMethodEnum;
import com.nnnmkj.thai.module.quiz.enums.TaskRecordStatusEnum;
import com.nnnmkj.thai.module.quiz.enums.TaskStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import reactor.core.publisher.Flux;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.quiz.enums.ErrorCodeConstants.AI_QUESTION_GENERATE_CONFIG_NOT_EXISTS;

/**
 * AI出题生成配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AiQuestionGenerateConfigServiceImpl implements AiQuestionGenerateConfigService {

    @Resource
    private AiQuestionGenerateConfigMapper aiQuestionGenerateConfigMapper;

    @Resource
    private AiQuestionGenerateTaskMapper aiQuestionGenerateTaskMapper;

    @Resource
    private AiQuestionGenerateTaskRecordMapper aiQuestionGenerateTaskRecordMapper;

    @Resource
    private AiModelApi aiModelApi;

    @Resource
    private AiModelFactory modelFactory;

    @Resource
    private QuestionBankMapper questionBankMapper;

    @Resource
    private QuestionBankKnowledgePointMapper questionBankKnowledgePointMapper;
    @Resource
    private QuestionBankOptionMapper questionBankOptionMapper;

    @Resource
    private AiQuestionMapper aiQuestionMapper;

    private static final String MODEL_TYPE = "AI生成试题";

    @Override
    public Long createAiQuestionGenerateConfig(AiQuestionGenerateConfigSaveReqVO createReqVO) {
        // 插入
        AiQuestionGenerateConfigDO aiQuestionGenerateConfig = BeanUtils.toBean(createReqVO, AiQuestionGenerateConfigDO.class);
        aiQuestionGenerateConfigMapper.insert(aiQuestionGenerateConfig);
        // 返回
        return aiQuestionGenerateConfig.getId();
    }

    @Override
    public void updateAiQuestionGenerateConfig(AiQuestionGenerateConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateAiQuestionGenerateConfigExists(updateReqVO.getId());
        // 更新
        AiQuestionGenerateConfigDO updateObj = BeanUtils.toBean(updateReqVO, AiQuestionGenerateConfigDO.class);
        aiQuestionGenerateConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteAiQuestionGenerateConfig(Long id) {
        // 校验存在
        validateAiQuestionGenerateConfigExists(id);
        // 删除
        aiQuestionGenerateConfigMapper.deleteById(id);
    }

    private void validateAiQuestionGenerateConfigExists(Long id) {
        if (aiQuestionGenerateConfigMapper.selectById(id) == null) {
            throw exception(AI_QUESTION_GENERATE_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public AiQuestionGenerateConfigDO getAiQuestionGenerateConfig(Long id) {
        return aiQuestionGenerateConfigMapper.selectById(id);
    }

    @Override
    public AiQuestionGenerateConfigDO getAiQuestionGenerateConfigByQuestionId(Long questionId) {
        return aiQuestionGenerateConfigMapper.selectFirstOne(AiQuestionGenerateConfigDO::getAiQuestionId, questionId);
    }

    @Override
    public AiQuestionGenerateConfigDO getCurrentConfig(AiQuestionGenerateConfigSaveReqVO createReqVO) {
        Long questionId = createReqVO.getAiQuestionId();
        AiQuestionGenerateConfigDO configDO = aiQuestionGenerateConfigMapper.selectFirstOne(AiQuestionGenerateConfigDO::getAiQuestionId, questionId);
        if (configDO == null) {
            // 插入
            configDO = BeanUtils.toBean(createReqVO, AiQuestionGenerateConfigDO.class);
            aiQuestionGenerateConfigMapper.insert(configDO);
        }
        return configDO;
    }

    @Override
    public Flux<CommonResult<String>> generate(Long aiQuestionId, Integer difficulty, Integer questionType, List<QuestionGenerateDTO> list, Long userId, Long taskId) {
        try {
            // 成功数量
            Long successCount = 0L;
            // 失败数量
            Long errorCount = 0L;
            // 遍历列表进行题目生成
            for (QuestionGenerateDTO questionGenerateDTO : list) {
                // 初始化任务记录
                AiQuestionGenerateTaskRecordDO generateTaskRecordDO = new AiQuestionGenerateTaskRecordDO();
                generateTaskRecordDO.setAiQuestionId(aiQuestionId);
                generateTaskRecordDO.setTaskId(taskId);
                generateTaskRecordDO.setQuestionType(questionType);
                generateTaskRecordDO.setDifficulty(difficulty);
                String sourceText = questionGenerateDTO.getSourceText();
                generateTaskRecordDO.setSourceText(sourceText);
                String keywords = questionGenerateDTO.getKeywords();
                generateTaskRecordDO.setKeywords(keywords);
                try {
                    // 构建请求对象
                    FastGPTRequestVO request = FastGPTRequestVO.createDefault()
                            .setChatId(IdUtil.getSnowflakeNextId())
                            .setCustomUid(userId)
                            .addUserMessage(questionGenerateDTO.getSourceText())
                            .addVariable("sourceText", sourceText)
                            .addVariable("keywords", keywords)
                            .addVariable("questionType", questionType)
                            .addVariable("difficulty", difficulty);
                    // 发送AI请求
                    String response = sendFastGPTRequest(request);
                    // 记录generate响应日志
                    log.info("[generate] 响应: {}", response);
                    if (StrUtil.isNotBlank(response)) {
                        // 解析响应数据，并插入数据库
                        parseResponse(aiQuestionId, difficulty, questionType, response);
                    }
                    successCount++;
                    generateTaskRecordDO.setTaskRecordStatus(TaskRecordStatusEnum.SUCCESS.getType());
                    aiQuestionGenerateTaskRecordMapper.insert(generateTaskRecordDO);
                } catch (Exception e) {
                    errorCount++;
                    generateTaskRecordDO.setTaskRecordStatus(TaskRecordStatusEnum.FAIL.getType());
                    generateTaskRecordDO.setFailReason(e.getMessage());
                    aiQuestionGenerateTaskRecordMapper.insert(generateTaskRecordDO);
                }
            }
            // 更新生成任务
            AiQuestionGenerateTaskDO generateTaskDO = new AiQuestionGenerateTaskDO();
            generateTaskDO.setId(taskId);
            generateTaskDO.setSuccessCount(successCount);
            generateTaskDO.setErrorCount(errorCount);
            generateTaskDO.setStatus(TaskStatusEnum.COMPLETE.getType());
            insertOrUpdateTask(generateTaskDO);
            // 更新AI生成试题状态
            AiQuestionDO aiQuestionDO = aiQuestionMapper.selectById(aiQuestionId);
            if (aiQuestionDO != null) {
                aiQuestionDO.setStatus(AiQuestionStatusEnum.YES.getType());
                aiQuestionMapper.updateById(aiQuestionDO);
            }
        } catch (Exception e) {
            log.error("生成失败", e);
        }
        return Flux.just(CommonResult.success());
    }

    /**
     * 解析AI响应数据并插入数据库
     *
     * @param questionId   AI生成题目ID
     * @param difficulty   难易度
     * @param questionType 题目类型
     * @param response     AI响应数据
     */
    private void parseResponse(Long questionId, Integer difficulty, Integer questionType, String response) {
        // 直接将响应字符串解析为JSONObject对象，避免使用泛型Map
        JSONObject responseObj = JsonUtils.parseObject(response, JSONObject.class);
        // 创建响应对象实例
        QuestionBankDO questionBankDO = new QuestionBankDO();
        questionBankDO.setAiQuestionId(questionId);
        questionBankDO.setDifficulty(difficulty);
        questionBankDO.setQuestionType(questionType);
        questionBankDO.setCreateMethod(CreateMethodEnum.AI.getType());
        if (responseObj != null) {
            questionBankDO.setQuestionStem(responseObj.getStr("question"));
        }
        if (responseObj != null) {
            questionBankDO.setAnswer(responseObj.getStr("answer"));
        }
        if (responseObj != null) {
            questionBankDO.setSource(responseObj.getStr("source"));
        }
        if (responseObj != null) {
            questionBankDO.setAnalysis(responseObj.getStr("questionAnalysis"));
        }
        questionBankMapper.insert(questionBankDO);
        List<QuestionBankOptionDO> questionBankOptions = null;
        if (responseObj != null && responseObj.getJSONArray("optionsList") != null) {
            questionBankOptions = responseObj.getJSONArray("optionsList").toList(QuestionBankOptionDO.class);
        }
        if (CollUtil.isNotEmpty(questionBankOptions)) {
            questionBankOptions.forEach(o -> o.setQuestionId(questionBankDO.getId()));
            questionBankOptionMapper.insertBatch(questionBankOptions);
        }
        List<QuestionBankKnowledgePointDO> questionBankKnowledgePoints = null;
        if (responseObj != null && responseObj.getJSONArray("knowledgePoints") != null) {
            questionBankKnowledgePoints = responseObj.getJSONArray("knowledgePoints").toList(QuestionBankKnowledgePointDO.class);
        }
        String answer = questionBankDO.getAnswer();
        // 如果答案为选项、找到答案的选项内容,添加为知识点。如果答案不是选项，则直接添加答案为知识点
        if (StringUtils.isNotBlank(answer)) {
            if (answer.matches("^[A-E]$")) {
                if (questionBankOptions != null) {
                    for (QuestionBankOptionDO questionBankOptionDO : questionBankOptions) {
                        if (questionBankOptionDO.getContent().equals(answer)) {
                            if (questionBankKnowledgePoints != null) {
                                questionBankKnowledgePoints.add(QuestionBankKnowledgePointDO.builder().questionId(questionBankDO.getId()).content(questionBankOptionDO.getContent()).build());
                            }
                        }
                    }
                }
            } else {
                if (questionBankKnowledgePoints != null) {
                    questionBankKnowledgePoints.add(QuestionBankKnowledgePointDO.builder().questionId(questionBankDO.getId()).content(answer).build());
                }
            }
        }

        if (CollUtil.isNotEmpty(questionBankKnowledgePoints)) {
            questionBankKnowledgePoints.forEach(o -> o.setQuestionId(questionBankDO.getId()));
            questionBankKnowledgePointMapper.insertBatch(questionBankKnowledgePoints);
        }
    }

    /**
     * 插入生成任务
     *
     * @param generateTaskDO 任务DO
     */
    private void insertOrUpdateTask(AiQuestionGenerateTaskDO generateTaskDO) {
        if (generateTaskDO.getId() != null) {
            aiQuestionGenerateTaskMapper.updateById(generateTaskDO);
        } else {
            aiQuestionGenerateTaskMapper.insert(generateTaskDO);
        }
    }

    private String sendFastGPTRequest(FastGPTRequestVO request) {
        // 1. 获取模型对应的API Key
        AiModelVO aiModelVO = aiModelApi.selectModelByName(MODEL_TYPE);
        Long keyId = aiModelVO.getKeyId();
        if (ObjUtil.isNull(keyId)) {
            throw exception(ErrorCodeConstants.MODEL_NOT_EXISTS);
        }
        AiApiKeyVO aiApiKeyVO = aiModelApi.selectApiKeyById(keyId);
        if (aiApiKeyVO == null || ObjUtil.notEqual(aiApiKeyVO.getPlatform(), AiPlatformEnum.FAST_GPT.getPlatform())) {
            throw exception(ErrorCodeConstants.MODEL_API_KEY_NOT_MATCH);
        }
        // 2. 创建FastGPTApi实例
        FastGPTApi fastGPTApi = modelFactory.getOrCreateFastGPTApi(aiApiKeyVO.getApiKey(), aiApiKeyVO.getUrl());

        // 3. 调用FastGPT方法发送请求
        String response = fastGPTApi.FastGPTAsOpenAI(request);
        log.info("来自 FastGPT 的响应: {}", response);

        // 4. 解析响应内容
        String content = preprocessJsonUtil.preprocessJsonContent(response);
        log.info("处理后的内容: {}", content);
        return content;
    }

    @Override
    public PageResult<AiQuestionGenerateConfigDO> getAiQuestionGenerateConfigPage(AiQuestionGenerateConfigPageReqVO pageReqVO) {
        return aiQuestionGenerateConfigMapper.selectPage(pageReqVO);
    }

}