package com.nnnmkj.thai.module.quiz.dal.mysql.questionBank;

import cn.hutool.core.collection.CollUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo.QuestionBankPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.app.questionBank.vo.AppQuestionBankPageReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 题目 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionBankMapperX extends BaseMapperX<QuestionBankDTO> {
    default PageResult<QuestionBankDTO> selectPage(QuestionBankPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<QuestionBankDTO> wrapper = new MPJLambdaWrapperX<QuestionBankDTO>()
                .selectAll(QuestionBankDO.class)
                .selectAs("t1.nickname", QuestionBankDTO::getNickname)
                .eqIfPresent(QuestionBankDO::getAiQuestionId, reqVO.getAiQuestionId())
                .eqIfPresent(QuestionBankDO::getQuestionType, reqVO.getQuestionType())
                .likeIfPresent(QuestionBankDO::getQuestionStem, reqVO.getQuestionStem())
                .likeIfPresent(QuestionBankDO::getAnswer, reqVO.getAnswer())
                .likeIfPresent(QuestionBankDO::getSource, reqVO.getSource())
                .likeIfPresent(QuestionBankDO::getAnalysis, reqVO.getAnalysis())
                .eqIfPresent(QuestionBankDO::getDifficulty, reqVO.getDifficulty())
                .likeIfPresent(QuestionBankDO::getRejectReason, reqVO.getRejectReason())
                .eqIfPresent(QuestionBankDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(QuestionBankDO::getPublicStatus, reqVO.getPublicStatus())
                .betweenIfPresent(QuestionBankDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(QuestionBankDO::getCreator, creatorQuery)
                .orderByDesc(QuestionBankDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)");
        return selectPage(reqVO, wrapper);
    }

    default PageResult<QuestionBankDTO> selectPage(AppQuestionBankPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        MPJLambdaWrapper<QuestionBankDTO> wrapper = new MPJLambdaWrapperX<QuestionBankDTO>()
                .selectAll(QuestionBankDO.class)
                .selectAs("t1.nickname", QuestionBankDTO::getNickname)
                .eqIfPresent(QuestionBankDO::getAiQuestionId, reqVO.getAiQuestionId())
                .eqIfPresent(QuestionBankDO::getQuestionType, reqVO.getQuestionType())
                .likeIfPresent(QuestionBankDO::getQuestionStem, reqVO.getQuestionStem())
                .likeIfPresent(QuestionBankDO::getAnswer, reqVO.getAnswer())
                .likeIfPresent(QuestionBankDO::getSource, reqVO.getSource())
                .likeIfPresent(QuestionBankDO::getAnalysis, reqVO.getAnalysis())
                .eqIfPresent(QuestionBankDO::getDifficulty, reqVO.getDifficulty())
                .likeIfPresent(QuestionBankDO::getRejectReason, reqVO.getRejectReason())
                .eqIfPresent(QuestionBankDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(QuestionBankDO::getPublicStatus, reqVO.getPublicStatus())
                .betweenIfPresent(QuestionBankDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(QuestionBankDO::getCreator, creatorQuery)
                .orderByDesc(QuestionBankDO::getId)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)");
        return selectPage(reqVO, wrapper);
    }

    default QuestionBankDTO selectById(Long id) {
        MPJLambdaWrapper<QuestionBankDTO> wrapper = new MPJLambdaWrapperX<QuestionBankDTO>()
                .selectAll(QuestionBankDO.class)
                .eq(QuestionBankDO::getId, id)
                .selectAs("t1.nickname", QuestionBankDTO::getNickname)
                .leftJoin("system_users t1 ON (t.creator LIKE concat('%su_id:', t1.id , '|%') AND t1.deleted = 0)");
        List<QuestionBankDTO> questionBankDTOS = selectList(wrapper);
        return CollUtil.getFirst(questionBankDTOS);
    }

}