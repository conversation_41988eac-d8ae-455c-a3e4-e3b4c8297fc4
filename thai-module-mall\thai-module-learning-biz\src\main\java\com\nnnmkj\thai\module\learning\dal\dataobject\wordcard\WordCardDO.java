package com.nnnmkj.thai.module.learning.dal.dataobject.wordcard;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 单词卡 DO
 *
 * <AUTHOR>
 */
@TableName("learning_word_card")
@KeySequence("learning_word_card_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WordCardDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 学习集编号
     */
    private Long wordSetId;
    /**
     * 词语
     */
    private String word;
    /**
     * 音标
     */
    private String phoneticSymbol;
    /**
     * 图片URL
     */
    private String imageUrl;
    /**
     * 音频URL
     */
    private String audioUrl;

}