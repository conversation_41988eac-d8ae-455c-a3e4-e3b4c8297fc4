package com.nnnmkj.thai.module.course.service.lessoncollection;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.web.core.util.WebFrameworkUtils;
import com.nnnmkj.thai.module.course.controller.app.lessoncollection.vo.AppLessonCollectionSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lessoncollection.LessonCollectionDO;
import com.nnnmkj.thai.module.course.dal.mysql.lessoncollection.LessonCollectionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.course.enums.ErrorCodeConstants.LESSON_COLLECTION_NOT_EXISTS;

/**
 * 课程收藏 Service 实现类 (APP)
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppLessonCollectionServiceImpl implements AppLessonCollectionService {

    @Resource
    private LessonCollectionMapper lessonCollectionMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createLessonCollection(AppLessonCollectionSaveReqVO createReqVO) {
        // 插入
        LessonCollectionDO lessonCollection = BeanUtils.toBean(createReqVO, LessonCollectionDO.class);
        lessonCollectionMapper.insert(lessonCollection);
        
        // 返回
        return lessonCollection.getId();
    }

    @Override
    public List<LessonCollectionDO> getLessonCollectionList(Collection<Long> lessonIds, Long userId) {
        if (CollUtil.isEmpty(lessonIds)) {
            return Collections.emptyList();
        }
        return lessonCollectionMapper.selectList(new LambdaQueryWrapperX<LessonCollectionDO>()
                .in(LessonCollectionDO::getLessonId, lessonIds)
                .eq(LessonCollectionDO::getUserId, userId));
    }

    @Override
    public List<LessonCollectionDO> getLessonCollectionListByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        String creatorQuery = CommonUtils.getCreatorQuery(userId, WebFrameworkUtils.getLoginUserType());
        if (!StringUtils.hasText(creatorQuery)) {
            return Collections.emptyList();
        }
        return lessonCollectionMapper.selectList(new LambdaQueryWrapperX<LessonCollectionDO>()
                .likeIfPresent(LessonCollectionDO::getCreator, creatorQuery)
                .orderByDesc(LessonCollectionDO::getCreateTime));
    }

    @Override
    public boolean isUserCollectedLesson(Long lessonId, Long userId, Integer userType) {
        if (lessonId == null || userId == null) {
            return false;
        }
        // 查询是否存在收藏记录
        String creatorQuery = CommonUtils.getCreatorQuery(userId, userType);
        List<LessonCollectionDO> collections = lessonCollectionMapper.selectList(
                LessonCollectionDO::getLessonId, lessonId,
                LessonCollectionDO::getCreator, creatorQuery);
        
        return CollUtil.isNotEmpty(collections);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLessonCollection(Long lessonId, Long userId) {
        // 校验存在
        validateLessonCollectionExists(lessonId, userId);
        // 删除
        lessonCollectionMapper.delete(new LambdaQueryWrapperX<LessonCollectionDO>()
                .eq(LessonCollectionDO::getLessonId, lessonId)
                .eq(LessonCollectionDO::getUserId, userId));
    }

    private void validateLessonCollectionExists(Long lessonId, Long userId) {
        List<LessonCollectionDO> lessonCollectionDOS = lessonCollectionMapper
                .selectList(LessonCollectionDO::getLessonId, lessonId, LessonCollectionDO::getUserId, userId);
        if (CollUtil.isEmpty(lessonCollectionDOS)) {
            throw exception(LESSON_COLLECTION_NOT_EXISTS);
        }
    }
} 