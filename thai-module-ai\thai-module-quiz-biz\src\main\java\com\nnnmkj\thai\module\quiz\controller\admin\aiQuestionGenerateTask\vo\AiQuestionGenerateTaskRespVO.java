package com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.nnnmkj.thai.framework.excel.core.annotations.DictFormat;
import com.nnnmkj.thai.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI出题生成任务 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AiQuestionGenerateTaskRespVO {

    @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24790")
    @ExcelProperty("任务ID")
    private Long id;

    @Schema(description = "AI出题ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1378")
    @ExcelProperty("AI出题ID")
    private Long aiQuestionId;

    @Schema(description = "AI出题名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1378")
    @ExcelProperty("AI出题名称")
    private String aiQuestionTitle;

    @Schema(description = "题目类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "题目类型", converter = DictConvert.class)
    @DictFormat("assignment_question_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer questionType;

    @Schema(description = "难易度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "难易度", converter = DictConvert.class)
    @DictFormat("assignment_question_difficulty") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer difficulty;

    @Schema(description = "总生成数量", example = "27513")
    @ExcelProperty("总生成数量")
    private Long allCount;

    @Schema(description = "成功数量", example = "30906")
    @ExcelProperty("成功数量")
    private Long successCount;

    @Schema(description = "失败数量", example = "5312")
    @ExcelProperty("失败数量")
    private Long errorCount;

    @Schema(description = "任务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "任务状态", converter = DictConvert.class)
    @DictFormat("task_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}