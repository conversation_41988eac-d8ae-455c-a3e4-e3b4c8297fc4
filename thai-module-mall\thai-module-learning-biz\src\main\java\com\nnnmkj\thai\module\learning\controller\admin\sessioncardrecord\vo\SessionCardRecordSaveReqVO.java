package com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 单词卡记录新增/修改 Request VO")
@Data
public class SessionCardRecordSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "26616")
    private Long id;

    @Schema(description = "学习集ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16114")
    @NotNull(message = "学习集ID不能为空")
    private Long setId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "546")
    private Long userId;

    @Schema(description = "学习会话ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20827")
    @NotNull(message = "学习会话ID不能为空")
    private Long sessionId;

    @Schema(description = "单词卡ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5963")
    @NotNull(message = "单词卡ID不能为空")
    private Long wordCardId;

    @Schema(description = "单词卡状态", example = "1")
    private Integer status;

}