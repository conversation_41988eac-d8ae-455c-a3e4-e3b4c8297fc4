package com.nnnmkj.thai.module.learning.service.sessionmatchrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDTO;
import com.nnnmkj.thai.module.learning.dal.mysql.sessionmatchrecord.SessionMatchRecordMapper;
import com.nnnmkj.thai.module.learning.dal.mysql.sessionmatchrecord.SessionMatchRecordMapperX;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.learning.enums.ErrorCodeConstants.SESSION_MATCH_RECORD_NOT_EXISTS;

/**
 * 配对记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SessionMatchRecordServiceImpl implements SessionMatchRecordService {

    @Resource
    private SessionMatchRecordMapper sessionMatchRecordMapper;

    @Resource
    private SessionMatchRecordMapperX sessionMatchRecordMapperX;

    @Override
    public Long createSessionMatchRecord(SessionMatchRecordSaveReqVO createReqVO) {
        // 插入
        SessionMatchRecordDO sessionMatchRecord = BeanUtils.toBean(createReqVO, SessionMatchRecordDO.class);
        sessionMatchRecordMapper.insert(sessionMatchRecord);
        // 返回
        return sessionMatchRecord.getId();
    }

    @Override
    public void updateSessionMatchRecord(SessionMatchRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateSessionMatchRecordExists(updateReqVO.getId());
        // 更新
        SessionMatchRecordDO updateObj = BeanUtils.toBean(updateReqVO, SessionMatchRecordDO.class);
        sessionMatchRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteSessionMatchRecord(Long id) {
        // 校验存在
        validateSessionMatchRecordExists(id);
        // 删除
        sessionMatchRecordMapper.deleteById(id);
    }

    private void validateSessionMatchRecordExists(Long id) {
        if (sessionMatchRecordMapper.selectById(id) == null) {
            throw exception(SESSION_MATCH_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public SessionMatchRecordDO getSessionMatchRecord(Long id) {
        return sessionMatchRecordMapper.selectById(id);
    }

    @Override
    public PageResult<SessionMatchRecordDTO> getSessionMatchRecordPage(SessionMatchRecordPageReqVO pageReqVO) {
        return sessionMatchRecordMapperX.selectPage(pageReqVO);
    }

    @Override
    public List<SessionMatchRecordDO> getSessionMatchRecordList(Long sessionId) {
        return sessionMatchRecordMapper.selectList(SessionMatchRecordDO::getSessionId, sessionId);
    }

}