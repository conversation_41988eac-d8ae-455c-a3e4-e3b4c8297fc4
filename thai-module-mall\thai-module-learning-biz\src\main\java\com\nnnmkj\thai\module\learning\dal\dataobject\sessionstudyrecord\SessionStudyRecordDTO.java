package com.nnnmkj.thai.module.learning.dal.dataobject.sessionstudyrecord;

import com.nnnmkj.thai.module.learning.dal.dataobject.questionoption.QuestionOptionDO;
import com.nnnmkj.thai.module.learning.enums.QuestionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 学习记录 DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SessionStudyRecordDTO extends SessionStudyRecordDO {
    /**
     * 题目类型
     * 枚举 {@link QuestionTypeEnum }
     */
    private Integer questionType;

    /**
     * 题目内容
     */
    private String content;
    /**
     * 参考答案
     */
    private String answer;
    /**
     * 学习集标题
     */
    private String setTitle;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 题目选项
     */
    private List<QuestionOptionDO> options;

}