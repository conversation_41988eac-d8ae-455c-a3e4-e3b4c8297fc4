package com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsetcollection.vo.WordSetCollectionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsetcollection.WordSetCollectionDTO;
import com.nnnmkj.thai.module.learning.service.wordsetcollection.WordSetCollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 学习集收藏")
@RestController
@RequestMapping("/learning/word-set-collection")
@Validated
public class WordSetCollectionController {

    @Resource
    private WordSetCollectionService wordSetCollectionService;

    @PostMapping("/create")
    @Operation(summary = "创建学习集收藏")
    @PreAuthorize("@ss.hasPermission('learning:word-set-collection:create')")
    public CommonResult<Long> createWordSetCollection(@Valid @RequestBody WordSetCollectionSaveReqVO createReqVO) {
        return success(wordSetCollectionService.createWordSetCollection(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新学习集收藏")
    @PreAuthorize("@ss.hasPermission('learning:word-set-collection:update')")
    public CommonResult<Boolean> updateWordSetCollection(@Valid @RequestBody WordSetCollectionSaveReqVO updateReqVO) {
        wordSetCollectionService.updateWordSetCollection(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除学习集收藏")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:word-set-collection:delete')")
    public CommonResult<Boolean> deleteWordSetCollection(@RequestParam("id") Long id) {
        wordSetCollectionService.deleteWordSetCollection(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得学习集收藏")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:word-set-collection:query')")
    public CommonResult<WordSetCollectionRespVO> getWordSetCollection(@RequestParam("id") Long id) {
        WordSetCollectionDO wordSetCollection = wordSetCollectionService.getWordSetCollection(id);
        return success(BeanUtils.toBean(wordSetCollection, WordSetCollectionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得学习集收藏分页")
    @PreAuthorize("@ss.hasPermission('learning:word-set-collection:query')")
    public CommonResult<PageResult<WordSetCollectionRespVO>> getWordSetCollectionPage(@Valid WordSetCollectionPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<WordSetCollectionDTO> pageResult = wordSetCollectionService.getWordSetCollectionPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, WordSetCollectionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出学习集收藏 Excel")
    @PreAuthorize("@ss.hasPermission('learning:word-set-collection:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWordSetCollectionExcel(@Valid WordSetCollectionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        List<WordSetCollectionDTO> list = wordSetCollectionService.getWordSetCollectionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "学习集收藏.xls", "数据", WordSetCollectionRespVO.class,
                        BeanUtils.toBean(list, WordSetCollectionRespVO.class));
    }

}