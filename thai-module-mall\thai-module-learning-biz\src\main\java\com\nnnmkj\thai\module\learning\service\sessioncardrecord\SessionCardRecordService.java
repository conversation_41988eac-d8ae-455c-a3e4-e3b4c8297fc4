package com.nnnmkj.thai.module.learning.service.sessioncardrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.sessioncardrecord.vo.SessionCardRecordSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessioncardrecord.SessionCardRecordDTO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 单词卡记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SessionCardRecordService {

    /**
     * 创建单词卡记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSessionCardRecord(@Valid SessionCardRecordSaveReqVO createReqVO);

    /**
     * 更新单词卡记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSessionCardRecord(@Valid SessionCardRecordSaveReqVO updateReqVO);

    /**
     * 删除单词卡记录
     *
     * @param id 编号
     */
    void deleteSessionCardRecord(Long id);

    /**
     * 获得单词卡记录
     *
     * @param id 编号
     * @return 单词卡记录
     */
    SessionCardRecordDO getSessionCardRecord(Long id);

    /**
     * 获得单词卡记录分页
     *
     * @param pageReqVO 分页查询
     * @return 单词卡记录分页
     */
    PageResult<SessionCardRecordDTO> getSessionCardRecordPage(SessionCardRecordPageReqVO pageReqVO);

    /**
     * 获得单词卡记录列表
     * @param sessionId 会话ID
     * @return 单词卡记录列表
     */
    List<SessionCardRecordDO> getSessionCardRecordList(Long sessionId);

}