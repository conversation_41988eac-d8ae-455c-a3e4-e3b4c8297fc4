package com.nnnmkj.thai.module.learning.controller.admin.wordsettop;

import cn.hutool.core.collection.CollUtil;
import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordsettop.vo.WordSetTopSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordsettop.WordSetTopDTO;
import com.nnnmkj.thai.module.learning.service.wordsettop.WordSetTopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 学习集置顶")
@RestController
@RequestMapping("/learning/word-set-top")
@Validated
public class WordSetTopController {

    @Resource
    private WordSetTopService wordSetTopService;

    @PostMapping("/create")
    @Operation(summary = "创建学习集置顶")
    @PreAuthorize("@ss.hasPermission('learning:word-set-top:create')")
    public CommonResult<Long> createWordSetTop(@Valid @RequestBody WordSetTopSaveReqVO createReqVO) {
        return success(wordSetTopService.createWordSetTop(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新学习集置顶")
    @PreAuthorize("@ss.hasPermission('learning:word-set-top:update')")
    public CommonResult<Boolean> updateWordSetTop(@Valid @RequestBody WordSetTopSaveReqVO updateReqVO) {
        wordSetTopService.updateWordSetTop(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除学习集置顶")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('learning:word-set-top:delete')")
    public CommonResult<Boolean> deleteWordSetTop(@RequestParam("id") Long id) {
        wordSetTopService.deleteWordSetTop(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得学习集置顶")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('learning:word-set-top:query')")
    public CommonResult<WordSetTopRespVO> getWordSetTop(@RequestParam("id") Long id) {
        WordSetTopDO wordSetTop = wordSetTopService.getWordSetTop(id);
        return success(BeanUtils.toBean(wordSetTop, WordSetTopRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得学习集置顶分页")
    @PreAuthorize("@ss.hasPermission('learning:word-set-top:query')")
    public CommonResult<PageResult<WordSetTopRespVO>> getWordSetTopPage(@Valid WordSetTopPageReqVO pageReqVO) {
        if (pageReqVO.getUserType() == null) {
            pageReqVO.setUserType(UserTypeEnum.ADMIN.getValue());
        }
        PageResult<WordSetTopDTO> pageResult = wordSetTopService.getWordSetTopPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return success(PageResult.empty());
        }
        return success(BeanUtils.toBean(pageResult, WordSetTopRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出学习集置顶 Excel")
    @PreAuthorize("@ss.hasPermission('learning:word-set-top:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWordSetTopExcel(@Valid WordSetTopPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WordSetTopDTO> list = wordSetTopService.getWordSetTopPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "学习集置顶.xls", "数据", WordSetTopRespVO.class,
                        BeanUtils.toBean(list, WordSetTopRespVO.class));
    }

}