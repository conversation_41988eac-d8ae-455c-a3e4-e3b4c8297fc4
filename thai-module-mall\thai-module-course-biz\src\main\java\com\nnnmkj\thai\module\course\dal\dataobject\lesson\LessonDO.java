package com.nnnmkj.thai.module.course.dal.dataobject.lesson;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 课程 DO
 *
 * <AUTHOR>
 */
@TableName("course_lesson")
@KeySequence("course_lesson_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LessonDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程标题
     */
    private String title;
    /**
     * 课程简介
     */
    private String description;
    /**
     * 课程封面图URL
     */
    private String coverImage;
    /**
     * 用户ID
     */
    @Deprecated
    private Long userId;

}