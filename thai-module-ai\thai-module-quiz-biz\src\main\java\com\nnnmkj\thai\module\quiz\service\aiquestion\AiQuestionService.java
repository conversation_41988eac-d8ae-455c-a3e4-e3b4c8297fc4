package com.nnnmkj.thai.module.quiz.service.aiquestion;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiquestion.vo.AiQuestionSaveReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDTO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * AI出题 Service 接口
 *
 * <AUTHOR>
 */
public interface AiQuestionService {

    /**
     * 创建AI出题
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAiQuestion(@Valid AiQuestionSaveReqVO createReqVO);

    /**
     * 更新AI出题
     *
     * @param updateReqVO 更新信息
     */
    void updateAiQuestion(@Valid AiQuestionSaveReqVO updateReqVO);

    /**
     * 删除AI出题
     *
     * @param id 编号
     */
    void deleteAiQuestion(Long id);

    /**
     * 获得AI出题
     *
     * @param id 编号
     * @return AI出题
     */
    AiQuestionDO getAiQuestion(Long id);

    /**
     * 获得AI出题分页
     *
     * @param pageReqVO 分页查询
     * @return AI出题分页
     */
    PageResult<AiQuestionDTO> getAiQuestionPage(AiQuestionPageReqVO pageReqVO);

    /**
     * 获得AI出题列表
     *
     * @param reqVO 列表查询
     * @return AI出题列表
     */
    List<AiQuestionDO> getAiQuestionList(AiQuestionPageReqVO reqVO);

}