package com.nnnmkj.thai.module.learning.controller.admin.sessiontestrecord.vo;

import com.nnnmkj.thai.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.nnnmkj.thai.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 测试记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SessionTestRecordPageReqVO extends PageParam {

    @Schema(description = "学习集ID", example = "16473")
    private Long setId;

    @Schema(description = "用户ID", example = "21799")
    private Long userId;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

    @Schema(description = "学习会话ID", example = "27526")
    private Long sessionId;

    @Schema(description = "题目ID", example = "29095")
    private Long questionId;

    @Schema(description = "用户答案", example = "A")
    private String userAnswer;

    @Schema(description = "是否正确")
    private Integer isCorrect;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}