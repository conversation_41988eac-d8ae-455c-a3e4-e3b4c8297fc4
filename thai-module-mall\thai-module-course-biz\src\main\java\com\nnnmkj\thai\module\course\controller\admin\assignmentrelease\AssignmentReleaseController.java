package com.nnnmkj.thai.module.course.controller.admin.assignmentrelease;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignmentrelease.vo.*;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentrelease.AssignmentReleaseDO;
import com.nnnmkj.thai.module.course.dal.dataobject.membergroupassignmentrelease.MemberGroupAssignmentReleaseDO;
import com.nnnmkj.thai.module.course.service.assignmentrelease.AssignmentReleaseService;
import com.nnnmkj.thai.module.course.service.membergroupassignmentrelease.MemberGroupAssignmentReleaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课程作业发布")
@RestController
@RequestMapping("/course/assignment-release")
@Validated
public class AssignmentReleaseController {

    @Resource
    private AssignmentReleaseService assignmentReleaseService;
    @Resource
    private MemberGroupAssignmentReleaseService memberGroupAssignmentReleaseService;

    @PostMapping("/create")
    @Operation(summary = "创建课程作业发布")
    @PreAuthorize("@ss.hasPermission('course:assignment-release:create')")
    public CommonResult<Long> createAssignmentRelease(@Valid @RequestBody AssignmentReleaseCreateReqVO createReqVO) {
        return success(assignmentReleaseService.createAssignmentRelease(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业发布")
    @PreAuthorize("@ss.hasPermission('course:assignment-release:update')")
    public CommonResult<Boolean> updateAssignmentRelease(@Valid @RequestBody AssignmentReleaseUpdateReqVO updateReqVO) {
        assignmentReleaseService.updateAssignmentRelease(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业发布")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('course:assignment-release:delete')")
    public CommonResult<Boolean> deleteAssignmentRelease(@RequestParam("id") Long id) {
        assignmentReleaseService.deleteAssignmentRelease(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课程作业发布")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('course:assignment-release:query')")
    public CommonResult<AssignmentReleaseDetailRespVO> getAssignmentRelease(@RequestParam("id") Long id) {
        AssignmentReleaseDO assignmentRelease = assignmentReleaseService.getAssignmentRelease(id);

        AssignmentReleaseDetailRespVO bean = BeanUtils.toBean(assignmentRelease, AssignmentReleaseDetailRespVO.class);
        MemberGroupAssignmentReleaseDO association = memberGroupAssignmentReleaseService.getMemberGroupAssignmentRelease(id);
        bean.setGroupIds(association.getGroupIds());
        bean.setUserIds(association.getUserIds());

        return success(bean);
    }

    @GetMapping("/page")
    @Operation(summary = "获得课程作业发布分页")
    @PreAuthorize("@ss.hasPermission('course:assignment-release:query')")
    public CommonResult<PageResult<AssignmentReleaseRespVO>> getAssignmentReleasePage(@Valid AssignmentReleasePageReqVO pageReqVO) {
        PageResult<AssignmentReleaseDO> pageResult = assignmentReleaseService.getAssignmentReleaseVOPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssignmentReleaseRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课程作业发布 Excel")
    @PreAuthorize("@ss.hasPermission('course:assignment-release:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAssignmentReleaseExcel(@Valid AssignmentReleasePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssignmentReleaseDO> list = assignmentReleaseService.getAssignmentReleaseVOPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课程作业发布.xls", "数据", AssignmentReleaseRespVO.class,
                        BeanUtils.toBean(list, AssignmentReleaseRespVO.class));
    }

}