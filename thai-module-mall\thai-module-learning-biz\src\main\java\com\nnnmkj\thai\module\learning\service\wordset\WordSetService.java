package com.nnnmkj.thai.module.learning.service.wordset;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetRespVO;
import com.nnnmkj.thai.module.learning.controller.admin.wordset.vo.WordSetSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordcard.WordCardDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.wordset.WordSetPageDTO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 学习集 Service 接口
 *
 * <AUTHOR>
 */
public interface WordSetService {

    /**
     * 创建学习集
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWordSet(@Valid WordSetSaveReqVO createReqVO);

    /**
     * 更新学习集
     *
     * @param updateReqVO 更新信息
     */
    void updateWordSet(@Valid WordSetSaveReqVO updateReqVO);

    /**
     * 删除学习集
     *
     * @param id 编号
     */
    void deleteWordSet(Long id);

    /**
     * 获得学习集
     *
     * @param id 编号
     * @return 学习集
     */
    WordSetDO getWordSet(Long id);

    /**
     * 获得学习集带单词卡和单词卡定义
     * @param id 编号
     * @return 学习集信息
     */
    WordSetRespVO getWordSetWithCardsAndDefinitions(Long id);

    /**
     * 获得学习集数量
     *
     * @return 学习集数量
     */
    Long getWordSetCount();

    /**
     * 获得学习集分页
     *
     * @param pageReqVO 分页查询
     * @return 学习集分页
     */
    PageResult<WordSetPageDTO> getWordSetPage(WordSetPageReqVO pageReqVO);

    /**
     * 获得学习集列表
     *
     * @param ids 学习集的数组
     * @return 学习集列表
     */
    List<WordSetDO> getWordSetList(Collection<Long> ids);

    /**
     * 获得学习集列表
     *
     * @param reqVO 列表查询
     * @return 学习集列表
     */
    List<WordSetDO> getWordSetList(WordSetPageReqVO reqVO);

    // ==================== 子表（单词卡） ====================

    /**
     * 获得单词卡列表
     *
     * @param wordSetId 学习集编号
     * @return 单词卡列表
     */
    List<WordCardDO> getWordCardListByWordSetId(Long wordSetId);

}