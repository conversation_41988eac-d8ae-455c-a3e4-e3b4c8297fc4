package com.nnnmkj.thai.module.quiz.dal.mysql.aiQuestionGenerateTask;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskPageReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiquestion.AiQuestionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI出题生成任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AiQuestionGenerateTaskMapperX extends BaseMapperX<AiQuestionGenerateTaskDTO> {

    default PageResult<AiQuestionGenerateTaskDTO> selectPage(AiQuestionGenerateTaskPageReqVO reqVO) {
        MPJLambdaWrapper<AiQuestionGenerateTaskDTO> wrapper = new MPJLambdaWrapperX<AiQuestionGenerateTaskDTO>()
                .selectAll(AiQuestionGenerateTaskDO.class)
                .selectAs(AiQuestionDO::getTitle, AiQuestionGenerateTaskDTO::getAiQuestionTitle)
                .eqIfPresent(AiQuestionGenerateTaskDO::getAiQuestionId, reqVO.getAiQuestionId())
                .eqIfPresent(AiQuestionGenerateTaskDO::getQuestionType, reqVO.getQuestionType())
                .eqIfPresent(AiQuestionGenerateTaskDO::getDifficulty, reqVO.getDifficulty())
                .eqIfPresent(AiQuestionGenerateTaskDO::getAllCount, reqVO.getAllCount())
                .eqIfPresent(AiQuestionGenerateTaskDO::getSuccessCount, reqVO.getSuccessCount())
                .eqIfPresent(AiQuestionGenerateTaskDO::getErrorCount, reqVO.getErrorCount())
                .eqIfPresent(AiQuestionGenerateTaskDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AiQuestionGenerateTaskDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AiQuestionGenerateTaskDO::getId)
                .leftJoin(AiQuestionDO.class, AiQuestionDO::getId, AiQuestionGenerateTaskDO::getAiQuestionId);
        return selectPage(reqVO, wrapper);
    }

}