package com.nnnmkj.thai.module.quiz.enums;

import com.nnnmkj.thai.framework.common.core.ArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 创建方式 0-个人，1-AI生成
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum CreateMethodEnum implements ArrayValuable<Integer> {

    PERSON(0, "个人"),
    AI(1, "AI生成");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(CreateMethodEnum::getType).toArray(Integer[]::new);

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 类型名
     */
    private final String name;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}
