package com.nnnmkj.thai.module.course.controller.app.assignmentrelease;

import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.app.assignmentrelease.vo.*;
import com.nnnmkj.thai.module.course.service.assignmentrelease.AssignmentReleaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;
import static com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 App - 课程作业发布")
@RestController
@RequestMapping("/course/assignment-release")
@Validated
public class AppAssignmentReleaseController {

    @Resource
    private AssignmentReleaseService assignmentReleaseService;

    @GetMapping("/page")
    @Operation(summary = "获得课程作业发布分页")
    public CommonResult<PageResult<AppAssignmentReleaseRespVO>> getAssignmentReleasePage(@Valid AppAssignmentReleasePageReqVO pageReqVO) {
        return success(assignmentReleaseService.getAssignmentReleaseVOPage(pageReqVO));
    }

    @GetMapping("/page/my")
    @Operation(summary = "获取当前用户的课程作业发布分页")
    @Parameter(name = "groupId", description = "班级编号", required = true)
    public CommonResult<PageResult<AppAssignmentReleaseSimpleRespVO>> getAssignmentReleasePageMy(
            @NotNull(message = "请选择班级") Long groupId,
            @Valid AppAssignmentReleasePageReqVO pageReqVO) {
        return success(assignmentReleaseService.getAssignmentReleaseSimpleVOPage(getLoginUserId(), groupId, pageReqVO));
    }

    @GetMapping("/get/my")
    @Operation(summary = "根据ID获取当前用户的课程作业发布")
    public CommonResult<AppAssignmentReleaseDetailRespVO> getAssignmentReleaseMy(@NotNull(message = "请选择作业") Long id) {
        return success(assignmentReleaseService.getAssignmentReleaseDetail(id, getLoginUserId()));
    }

    @PostMapping("/create")
    @Operation(summary = "创建课程作业发布")
    public CommonResult<Long> createAssignmentRelease(@Valid @RequestBody AppAssignmentReleaseCreateReqVO createReqVO) {
        return success(assignmentReleaseService.createAssignmentRelease(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课程作业发布")
    public CommonResult<Boolean> updateAssignmentRelease(@Valid @RequestBody AppAssignmentReleaseUpdateReqVO updateReqVO) {
        assignmentReleaseService.updateAssignmentRelease(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课程作业发布")
    @Parameter(name = "id", description = "课程作业发布编号", required = true)
    public CommonResult<Boolean> deleteAssignmentRelease(@NotNull(message = "请选择作业") Long id) {
        assignmentReleaseService.deleteAssignmentRelease(id, getLoginUserId());
        return success(true);
    }

    @PostMapping("/answer/submit")
    @Operation(summary = "学生提交作业答题")
    public CommonResult<Boolean> answerSubmitAssignmentRelease(@Valid @RequestBody AppAssignmentReleaseAnswerSubmitReqVO reqVO) {
        assignmentReleaseService.answerSubmitAssignmentRelease(getLoginUserId(), reqVO);
        return success(true);
    }

    @GetMapping("/answer/report")
    @Operation(summary = "学生查看答题报告")
    @Parameter(name = "id", description = "课程作业发布编号", required = true)
    public CommonResult<AppAssignmentReleaseAnswerReportRespVO> answerReportAssignmentRelease(@NotNull(message = "请选择作业") Long id) {
        return success(assignmentReleaseService.answerReportAssignmentRelease(id, getLoginUserId()));
    }

    @GetMapping("/answer/statistics")
    @Operation(summary = "教师查看答题统计报告")
    @Parameter(name = "assignmentReleaseId", description = "课程作业发布编号", required = true)
    @Parameter(name = "groupId", description = "班级编号", required = true)
    public CommonResult<AppAssignmentReleaseAnswerStatisticsRespVO> statisticsReportAssignmentRelease(
            @NotNull(message = "请选择作业") Long assignmentReleaseId,
            @NotNull(message = "请选择班级") Long groupId) {
        return success(assignmentReleaseService.statisticsReportAssignmentRelease(getLoginUserId(), groupId, assignmentReleaseId));
    }

}
