package com.nnnmkj.thai.module.course.service.lesson;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonPageReqVO;
import com.nnnmkj.thai.module.course.controller.admin.lesson.vo.LessonSaveReqVO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonAttachmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDTO;
import jakarta.validation.Valid;

import java.util.Collection;
import java.util.List;

/**
 * 课程 Service 接口
 *
 * <AUTHOR>
 */
public interface LessonService {

    /**
     * 创建课程
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLesson(@Valid LessonSaveReqVO createReqVO);

    /**
     * 更新课程
     *
     * @param updateReqVO 更新信息
     */
    void updateLesson(@Valid LessonSaveReqVO updateReqVO);

    /**
     * 删除课程
     *
     * @param id 编号
     */
    void deleteLesson(Long id);

    /**
     * 获得课程
     *
     * @param id 编号
     * @return 课程
     */
    LessonDO getLesson(Long id);

    /**
     * 获得课程分页
     *
     * @param pageReqVO 分页查询
     * @return 课程分页
     */
    PageResult<LessonDTO> getLessonPage(LessonPageReqVO pageReqVO);

    /**
     * 获得课程分页
     *
     * @param pageReqVO 分页查询
     * @return 课程分页
     */
    PageResult<LessonDTO> getLessonPage(Long userId, Integer userType, LessonPageReqVO pageReqVO);

    /**
     * 获得课程列表
     *
     * @param ids 学习集的数组
     * @return 课程列表
     */
    List<LessonDO> getLessonList(Collection<Long> ids);

    /**
     * 获取课程列表
     *
     * @param pageReqVO 查询对象
     * @return 课程列表
     */
    List<LessonDO> getLessonList(LessonPageReqVO pageReqVO);

    // ==================== 子表（课程附件） ====================

    /**
     * 获得课程附件列表
     *
     * @param courseId 课程ID
     * @return 课程附件列表
     */
    List<LessonAttachmentDO> getLessonAttachmentListByCourseId(Long courseId);

}