package com.nnnmkj.thai.module.learning.dal.mysql.sessionmatchrecord;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.common.CommonUtils;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.module.learning.controller.admin.sessionmatchrecord.vo.SessionMatchRecordPageReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.sessionmatchrecord.SessionMatchRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 配对记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SessionMatchRecordMapper extends BaseMapperX<SessionMatchRecordDO> {

    default PageResult<SessionMatchRecordDO> selectPage(SessionMatchRecordPageReqVO reqVO) {
        String creatorQuery = CommonUtils.getCreatorQuery(reqVO.getUserId(), reqVO.getUserType());
        return selectPage(reqVO, new LambdaQueryWrapperX<SessionMatchRecordDO>()
                .eqIfPresent(SessionMatchRecordDO::getSetId, reqVO.getSetId())
                .likeIfPresent(SessionMatchRecordDO::getCreator, creatorQuery)
                .eqIfPresent(SessionMatchRecordDO::getSessionId, reqVO.getSessionId())
                .eqIfPresent(SessionMatchRecordDO::getWord, reqVO.getWord())
                .eqIfPresent(SessionMatchRecordDO::getDefinition, reqVO.getDefinition())
                .eqIfPresent(SessionMatchRecordDO::getIsCorrect, reqVO.getIsCorrect())
                .betweenIfPresent(SessionMatchRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SessionMatchRecordDO::getId));
    }

}