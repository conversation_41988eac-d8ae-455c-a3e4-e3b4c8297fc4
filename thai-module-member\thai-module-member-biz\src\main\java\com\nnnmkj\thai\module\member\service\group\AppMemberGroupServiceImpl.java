package com.nnnmkj.thai.module.member.service.group;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.nnnmkj.thai.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.member.controller.admin.group.vo.MemberGroupUsersRespVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupCreateReqVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupUpdateReqVO;
import com.nnnmkj.thai.module.member.controller.app.group.vo.AppMemberGroupUserRespVO;
import com.nnnmkj.thai.module.member.convert.group.MemberGroupConvert;
import com.nnnmkj.thai.module.member.dal.dataobject.group.MemberGroupDO;
import com.nnnmkj.thai.module.member.dal.dataobject.user.MemberUserDO;
import com.nnnmkj.thai.module.member.dal.mysql.group.MemberGroupMapper;
import com.nnnmkj.thai.module.member.dal.mysql.user.MemberUserMapper;
import com.nnnmkj.thai.module.member.enums.ErrorCodeConstants;
import com.nnnmkj.thai.module.member.service.user.MemberUserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.nnnmkj.thai.module.member.enums.ErrorCodeConstants.*;

/**
 * 用户分组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppMemberGroupServiceImpl implements AppMemberGroupService {

    @Resource
    private MemberGroupMapper memberGroupMapper;
    @Resource
    private MemberUserMapper memberUserMapper;
    @Resource
    private MemberGroupService memberGroupService;
    @Resource
    private MemberUserService memberUserService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createGroup(AppMemberGroupCreateReqVO createReqVO) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

        // 插入
        MemberGroupDO group = MemberGroupConvert.INSTANCE.convert(createReqVO);
        group.setUserId(loginUserId);
        memberGroupMapper.insert(group);
        Long groupId = group.getId();

        // 更新用户的分组字段
        MemberUserDO user = memberUserMapper.selectById(loginUserId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        List<Long> userGroupIds = Optional
                .ofNullable(user.getGroupIds())
                .orElseGet(ArrayList::new);

        // 校验是否已存在
        if (!userGroupIds.contains(groupId)) {
            userGroupIds.add(groupId);
            user.setGroupIds(userGroupIds);
            memberUserMapper.updateById(user);
        }

        // 返回
        return groupId;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGroup(AppMemberGroupUpdateReqVO updateReqVO) {
        // 校验存在
        MemberGroupDO existingGroup = memberGroupMapper.selectById(updateReqVO.getId());
        if (existingGroup == null) {
            throw exception(GROUP_NOT_EXISTS);
        }

        // 更新用户的分组字段
        Long originalUserId = existingGroup.getUserId();
        MemberUserDO user = memberUserMapper.selectById(originalUserId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        List<Long> groupIds = user.getGroupIds();
        if (CollUtil.isNotEmpty(groupIds)) {
            List<Long> list = CollUtil.removeAny(groupIds, originalUserId);
            user.setGroupIds(list);
            memberUserMapper.updateById(user);
        }

        // 更新
        MemberGroupDO updateObj = MemberGroupConvert.INSTANCE.convert(updateReqVO);
        memberGroupMapper.updateById(updateObj);
    }

    @Override
    public void deleteGroup(Long id) {
        // 校验分组下是否有用户
        Long count = memberUserService.getUserCountByGroupId(id);
        if (count > 0) {
            throw exception(GROUP_HAS_USER);
        }
        // 删除
        memberGroupMapper.deleteById(id);
    }

    @Override
    public MemberGroupDO getGroup(Long id) {
        return memberGroupMapper.selectById(id);
    }

    void validateGroupExists(Long id) {
        if (memberGroupMapper.selectById(id) == null) {
            throw exception(GROUP_NOT_EXISTS);
        }
    }

    @Override
    public boolean joinGroup(Long groupId) {
        // 校验班级存在
        validateGroupExists(groupId);

        // 获取用户当前的分组列表
        List<Long> groupIds = getGroupIdsByLoginUser();

        // 如果groupIds为空，创建新列表
        if (groupIds == null) {
            groupIds = new ArrayList<>();
        }

        // 判断用户是否已经在这个班级中
        if (groupIds.contains(groupId)) {
            // 用户已在该分组中，直接返回成功
            return true;
        }

        // 将新班级id添加到用户分组列表中
        groupIds.add(groupId);

        // 更新用户信息
        MemberUserDO memberUserDO = new MemberUserDO();
        memberUserDO.setId(SecurityFrameworkUtils.getLoginUserId());
        memberUserDO.setGroupIds(groupIds);
        memberUserMapper.updateById(memberUserDO);
        return true;
    }

    @Override
    public boolean quitGroup(Long groupId) {
        // 校验班级存在
        validateGroupExists(groupId);

        // 获取用户当前的分组列表
        List<Long> groupIds = getGroupIdsByLoginUser();

        // 如果groupIds为空或不包含要退出的班级ID，则直接返回成功（用户本来就不在这个班级中）
        if (groupIds == null || !groupIds.contains(groupId)) {
            return true;
        }

        // 从用户分组列表中移除班级ID
        CollUtil.removeAny(groupIds, groupId);

        // 更新用户信息
        MemberUserDO memberUserDO = new MemberUserDO();
        memberUserDO.setId(SecurityFrameworkUtils.getLoginUserId());
        memberUserDO.setGroupIds(groupIds);
        memberUserMapper.updateById(memberUserDO);
        return true;
    }

    @Override
    public boolean kickFromGroup(Long userId, Long groupId) {
        // 校验被踢出的用户存在
        MemberUserDO user = memberUserMapper.selectById(userId);
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }

        // 校验班级存在
        MemberGroupDO group = memberGroupMapper.selectById(groupId);
        if (group == null) {
            throw exception(GROUP_NOT_EXISTS);
        }

        // 校验当前用户是否有权限踢人（需要是班级创建者）
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        Long makerId = group.getUserId();
        if (!Objects.equals(loginUserId, makerId)) {
            throw exception(MEMBER_NOT_EXISTS);
        }

        // 获取用户当前的分组列表
        List<Long> groupIds = user.getGroupIds();
        // 如果groupIds为空或不包含要退出的班级ID，则直接返回成功（用户本来就不在这个班级中）
        if (groupIds == null || !groupIds.contains(groupId)) {
            return true;
        }

        // 从用户分组列表中移除班级ID
        groupIds.remove(groupId);

        // 更新用户信息
        MemberUserDO memberUserDO = new MemberUserDO();
        memberUserDO.setId(userId);
        memberUserDO.setGroupIds(groupIds);
        memberUserMapper.updateById(memberUserDO);
        return true;
    }

    @Override
    public List<AppMemberGroupUserRespVO> getUserListByGroupId(Long groupId, Long userId) {
        List<MemberUserDO> memberUserDOS = memberUserMapper.selectListByUserIdAndGroupId(groupId, userId);
        return BeanUtil.copyToList(memberUserDOS, AppMemberGroupUserRespVO.class);
    }

    @Override
    public List<AppMemberGroupUserRespVO> getUserListByGroupIds(Collection<Long> groupIds, Long userId) {
        List<MemberUserDO> memberUserDOS = memberUserMapper.selectListByUserIdAndGroupIds(groupIds, userId);
        return BeanUtil.copyToList(memberUserDOS, AppMemberGroupUserRespVO.class);
    }

    @Override
    public List<MemberGroupDO> getGroupListByUserId(Long userId) {
        return memberGroupMapper.selectList(MemberGroupDO::getUserId, userId);
    }

    @Override
    public List<MemberGroupDO> getGroupList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ListUtil.empty();
        }
        return memberGroupMapper.selectBatchIds(ids);
    }

    @Override
    public List<MemberGroupDO> getGroupsByLoginUser() {
        List<Long> groupIds = getGroupIdsByLoginUser();
        if (CollUtil.isEmpty(groupIds)) {
            return ListUtil.empty();
        }
        return getGroupList(groupIds);
    }

    @Override
    public List<Long> getGroupIdsByLoginUser() {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            throw exception(GlobalErrorCodeConstants.UNAUTHORIZED);
        }
        MemberUserDO memberUserDO = memberUserMapper.selectById(loginUserId);
        if (memberUserDO == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return memberUserDO.getGroupIds();
    }

    @Override
    public void checkAdminPermission(Long groupId) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            throw exception(GlobalErrorCodeConstants.UNAUTHORIZED);
        }

        MemberGroupDO group = getGroup(groupId);
        if (group == null) {
            throw exception(ErrorCodeConstants.GROUP_NOT_EXISTS);
        }
        if (!Objects.equals(group.getUserId(), loginUserId)) {
            throw exception(GlobalErrorCodeConstants.FORBIDDEN);
        }
    }

    @Override
    public List<MemberGroupUsersRespVO> getGroupUsersList(Long userId) {
        List<MemberGroupDO> groupList = getGroupListByUserId(userId);

        if (CollUtil.isEmpty(groupList)) {
            return Collections.emptyList();
        }

        // 填充分组用户信息
        return memberGroupService.fillGroupUsersInfo(groupList);
    }
}
