package com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 课程作业发布题目新增/修改 Request VO")
@Data
public class AssignmentReleaseQuestionSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "7418")
    private Long id;

    @Schema(description = "课程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32557")
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    @Schema(description = "发布ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27852")
    @NotNull(message = "发布ID不能为空")
    private Long assignmentReleaseId;

    @Schema(description = "题目类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "题目类型不能为空")
    private Integer questionType;

    @Schema(description = "题干", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "题干不能为空")
    private String questionStem;

    @Schema(description = "参考答案", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "参考答案不能为空")
    private String answer;

    @Schema(description = "题目出处", requiredMode = Schema.RequiredMode.REQUIRED)
    private String source;

    @Schema(description = "试题解析", requiredMode = Schema.RequiredMode.REQUIRED)
    private String analysis;

    @Schema(description = "难易度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "难易度不能为空")
    private Integer difficulty;

    @Schema(description = "分数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分数不能为空")
    private Double score;
}