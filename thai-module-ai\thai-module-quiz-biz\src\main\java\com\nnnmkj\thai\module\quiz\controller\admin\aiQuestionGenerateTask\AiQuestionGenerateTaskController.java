package com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask;

import com.nnnmkj.thai.framework.apilog.core.annotation.ApiAccessLog;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageParam;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.object.BeanUtils;
import com.nnnmkj.thai.framework.excel.core.util.ExcelUtils;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskPageReqVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskRespVO;
import com.nnnmkj.thai.module.quiz.controller.admin.aiQuestionGenerateTask.vo.AiQuestionGenerateTaskSaveReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.aiQuestionGenerateTask.AiQuestionGenerateTaskRecordDO;
import com.nnnmkj.thai.module.quiz.service.aiQuestionGenerateTask.AiQuestionGenerateTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.nnnmkj.thai.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - AI出题生成任务")
@RestController
@RequestMapping("/quiz/ai-question-generate-task")
@Validated
public class AiQuestionGenerateTaskController {

    @Resource
    private AiQuestionGenerateTaskService aiQuestionGenerateTaskService;

    @PostMapping("/create")
    @Operation(summary = "创建AI出题生成任务")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question-generate-task:create')")
    public CommonResult<Long> createAiQuestionGenerateTask(@Valid @RequestBody AiQuestionGenerateTaskSaveReqVO createReqVO) {
        return success(aiQuestionGenerateTaskService.createAiQuestionGenerateTask(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新AI出题生成任务")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question-generate-task:update')")
    public CommonResult<Boolean> updateAiQuestionGenerateTask(@Valid @RequestBody AiQuestionGenerateTaskSaveReqVO updateReqVO) {
        aiQuestionGenerateTaskService.updateAiQuestionGenerateTask(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除AI出题生成任务")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('quiz:ai-question-generate-task:delete')")
    public CommonResult<Boolean> deleteAiQuestionGenerateTask(@RequestParam("id") Long id) {
        aiQuestionGenerateTaskService.deleteAiQuestionGenerateTask(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI出题生成任务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question-generate-task:query')")
    public CommonResult<AiQuestionGenerateTaskRespVO> getAiQuestionGenerateTask(@RequestParam("id") Long id) {
        AiQuestionGenerateTaskDO aiQuestionGenerateTask = aiQuestionGenerateTaskService.getAiQuestionGenerateTask(id);
        return success(BeanUtils.toBean(aiQuestionGenerateTask, AiQuestionGenerateTaskRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得AI出题生成任务分页")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question-generate-task:query')")
    public CommonResult<PageResult<AiQuestionGenerateTaskRespVO>> getAiQuestionGenerateTaskPage(@Valid AiQuestionGenerateTaskPageReqVO pageReqVO) {
        PageResult<AiQuestionGenerateTaskDTO> pageResult = aiQuestionGenerateTaskService.getAiQuestionGenerateTaskPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AiQuestionGenerateTaskRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出AI出题生成任务 Excel")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question-generate-task:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAiQuestionGenerateTaskExcel(@Valid AiQuestionGenerateTaskPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AiQuestionGenerateTaskDTO> list = aiQuestionGenerateTaskService.getAiQuestionGenerateTaskPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI出题生成任务.xls", "数据", AiQuestionGenerateTaskRespVO.class,
                        BeanUtils.toBean(list, AiQuestionGenerateTaskRespVO.class));
    }

    // ==================== 子表（AI出题生成任务记录） ====================

    @GetMapping("/ai-question-generate-task-record/list-by-task-id")
    @Operation(summary = "获得AI出题生成任务记录列表")
    @Parameter(name = "taskId", description = "任务ID")
    @PreAuthorize("@ss.hasPermission('quiz:ai-question-generate-task:query')")
    public CommonResult<List<AiQuestionGenerateTaskRecordDO>> getAiQuestionGenerateTaskRecordListByTaskId(@RequestParam("taskId") Long taskId) {
        return success(aiQuestionGenerateTaskService.getAiQuestionGenerateTaskRecordListByTaskId(taskId));
    }

}