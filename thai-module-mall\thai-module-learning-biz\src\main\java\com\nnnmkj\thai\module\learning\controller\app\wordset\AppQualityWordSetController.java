package com.nnnmkj.thai.module.learning.controller.app.wordset;

import com.nnnmkj.thai.framework.common.enums.UserTypeEnum;
import com.nnnmkj.thai.framework.common.pojo.CommonResult;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.security.core.util.SecurityFrameworkUtils;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetPageReqVO;
import com.nnnmkj.thai.module.learning.controller.app.wordset.vo.AppWordSetRespVO;
import com.nnnmkj.thai.module.learning.service.wordset.QualityWordSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.nnnmkj.thai.framework.common.pojo.CommonResult.success;

/**
 * 用户 APP - 优质学习集
 */
@Tag(name = "用户 APP - 优质学习集")
@RestController
@RequestMapping("/learning/quality-word-set")
@Validated
public class AppQualityWordSetController {

    @Resource
    private QualityWordSetService qualityWordSetService;

    @GetMapping("/page")
    @Operation(summary = "获取优质学习集分页")
    public CommonResult<PageResult<AppWordSetRespVO>> getQualityWordSets(@Valid AppWordSetPageReqVO pageReqVO) {
        pageReqVO.setUserType(UserTypeEnum.MEMBER.getValue());
        return success(qualityWordSetService.getQualityWordSets(pageReqVO));
    }

    @PostMapping("/view")
    @Operation(summary = "增加学习集点击量")
    @Parameter(name = "setId", description = "学习集ID", required = true, example = "1024")
    public CommonResult<Boolean> incrementViewCount(@RequestParam("setId") Long setId) {
        qualityWordSetService.incrementViewCount(setId, SecurityFrameworkUtils.getLoginUserId());
        return success(true);
    }
}
