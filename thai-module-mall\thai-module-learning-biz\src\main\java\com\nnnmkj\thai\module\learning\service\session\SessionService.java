package com.nnnmkj.thai.module.learning.service.session;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionPageReqVO;
import com.nnnmkj.thai.module.learning.controller.admin.session.vo.SessionSaveReqVO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDO;
import com.nnnmkj.thai.module.learning.dal.dataobject.session.SessionDTO;
import jakarta.validation.Valid;

/**
 * 学习会话 Service 接口
 *
 * <AUTHOR>
 */
public interface SessionService {

    /**
     * 创建学习会话
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSession(@Valid SessionSaveReqVO createReqVO);

    /**
     * 更新学习会话
     *
     * @param updateReqVO 更新信息
     */
    void updateSession(@Valid SessionSaveReqVO updateReqVO);

    /**
     * 删除学习会话
     *
     * @param id 编号
     */
    void deleteSession(Long id);

    /**
     * 获得学习会话
     *
     * @param id 编号
     * @return 学习会话
     */
    SessionDO getSession(Long id);

    /**
     * 获得学习会话分页
     *
     * @param pageReqVO 分页查询
     * @return 学习会话分页
     */
    PageResult<SessionDTO> getSessionPage(SessionPageReqVO pageReqVO);

}