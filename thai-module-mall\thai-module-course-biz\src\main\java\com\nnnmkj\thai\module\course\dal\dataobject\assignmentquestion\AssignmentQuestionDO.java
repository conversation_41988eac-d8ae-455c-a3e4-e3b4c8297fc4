package com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nnnmkj.thai.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 课程作业题目 DO
 *
 * <AUTHOR>
 */
@TableName("course_assignment_question")
@KeySequence("course_assignment_question_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentQuestionDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 作业ID
     */
    private Long assignmentId;
    /**
     * 题目类型
     *
     * 枚举 {@link TODO assignment_question_type 对应的类}
     */
    private Integer questionType;
    /**
     * 题干
     */
    private String questionStem;
    /**
     * 参考答案
     */
    private String answer;
    /**
     * 题目出处
     */
    private String source;
    /**
     * 试题解析
     */
    private String analysis;
    /**
     * 难易度
     *
     * 枚举 {@link TODO assignment_question_difficulty 对应的类}
     */
    private Integer difficulty;
    /**
     * 分数
     */
    private Double score;
}