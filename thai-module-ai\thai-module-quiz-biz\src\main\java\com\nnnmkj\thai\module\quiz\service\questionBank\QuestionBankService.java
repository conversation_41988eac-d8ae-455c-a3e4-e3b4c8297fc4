package com.nnnmkj.thai.module.quiz.service.questionBank;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo.*;
import com.nnnmkj.thai.module.quiz.controller.app.questionBank.vo.AppQuestionBankPageReqVO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBank.QuestionBankDTO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankKnowledgePoint.QuestionBankKnowledgePointDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankOption.QuestionBankOptionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankTag.QuestionBankTagDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 题目 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionBankService {

    /**
     * 创建题目
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createQuestionBank(@Valid QuestionBankSaveReqVO createReqVO);

    /**
     * 更新题目
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestionBank(@Valid QuestionBankSaveReqVO updateReqVO);

    /**
     * 更新题目审核
     * @param updateReqVO 更新信息
     */
    void auditQuestionBank(@Valid QuestionBankAuditReqVO updateReqVO);

    /**
     * 批量更新题目审核
     *@param reqVO 更新信息
     */
    void batchAuditQuestionBank(@Valid QuestionBankBatchAuditReqVO reqVO);

    /**
     * 批量添加标签
     * @param reqVO 更新信息
     */
    void batchAddTag(@Valid QuestionBankBatchAddTagReqVO reqVO);

    /**
     * 删除题目
     *
     * @param id 编号
     */
    void deleteQuestionBank(Long id);

    /**
     * 获得题目
     *
     * @param id 编号
     * @return 题目
     */
    QuestionBankDTO getQuestionBank(Long id);

    /**
     * 获得题目分页
     *
     * @param pageReqVO 分页查询
     * @return 题目分页
     */
    PageResult<QuestionBankDTO> getQuestionBankPage(QuestionBankPageReqVO pageReqVO);

    /**
     * 获得题目分页App
     *
     * @param pageReqVO 分页查询
     * @return 题目分页
     */
    PageResult<QuestionBankDTO> getQuestionBankPage(AppQuestionBankPageReqVO pageReqVO);

    /**
     * 获取题目列表
     *
     * @param ids 主键列表
     * @return 题目列表s
     */
    List<QuestionBankDO> getQuestionBankList(List<Long> ids);

    // ==================== 子表（题目知识点） ====================

    /**
     * 获得题目知识点列表
     *
     * @param questionId 题目ID
     * @return 题目知识点列表
     */
    List<QuestionBankKnowledgePointDO> getQuestionBankKnowledgePointListByQuestionId(Long questionId);

    /**
     * 获得题目知识点列表
     *
     * @param questionIds 题目ID列表
     * @return 题目知识点列表
     */
    List<QuestionBankKnowledgePointDO> getQuestionBankKnowledgePointListByQuestionIds(List<Long> questionIds);

    // ==================== 子表（题目选项） ====================

    /**
     * 获得题目选项列表
     *
     * @param questionId 题目ID
     * @return 题目选项列表
     */
    List<QuestionBankOptionDO> getQuestionBankOptionListByQuestionId(Long questionId);

    /**
     * 获得题目选项列表
     *
     * @param questionIds 题目ID列表
     * @return 题目选项列表
     */
    List<QuestionBankOptionDO> getQuestionBankOptionListByQuestionIds(List<Long> questionIds);

    // ==================== 子表（题目标签） ====================

    /**
     * 获得题目标签列表
     *
     * @param questionId 题目ID
     * @return 题目标签列表
     */
    List<QuestionBankTagDO> getQuestionBankTagListByQuestionId(Long questionId);

    /**
     * 获得题目标签列表
     *
     * @param questionIds 题目ID列表
     * @return 题目标签列表
     */
    List<QuestionBankTagDO> getQuestionBankTagListByQuestionIds(List<Long> questionIds);

}