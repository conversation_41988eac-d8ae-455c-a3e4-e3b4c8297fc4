package com.nnnmkj.thai.module.course.dal.mysql.assignmentreleasequestion;

import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nnnmkj.thai.framework.mybatis.core.mapper.BaseMapperX;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentreleasequestion.AssignmentReleaseQuestionDO;
import org.apache.ibatis.annotations.Mapper;
import com.nnnmkj.thai.module.course.controller.admin.assignmentreleasequestion.vo.*;

/**
 * 课程作业发布题目 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AssignmentReleaseQuestionMapper extends BaseMapperX<AssignmentReleaseQuestionDO> {

    default PageResult<AssignmentReleaseQuestionDO> selectPage(AssignmentReleaseQuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AssignmentReleaseQuestionDO>()
                .eqIfPresent(AssignmentReleaseQuestionDO::getCourseId, reqVO.getCourseId())
                .eqIfPresent(AssignmentReleaseQuestionDO::getAssignmentReleaseId, reqVO.getAssignmentReleaseId())
                .eqIfPresent(AssignmentReleaseQuestionDO::getQuestionType, reqVO.getQuestionType())
                .likeIfPresent(AssignmentReleaseQuestionDO::getQuestionStem, reqVO.getQuestionStem())
                .likeIfPresent(AssignmentReleaseQuestionDO::getAnswer, reqVO.getAnswer())
                .likeIfPresent(AssignmentReleaseQuestionDO::getSource, reqVO.getSource())
                .likeIfPresent(AssignmentReleaseQuestionDO::getAnalysis, reqVO.getAnalysis())
                .eqIfPresent(AssignmentReleaseQuestionDO::getDifficulty, reqVO.getDifficulty())
                .betweenIfPresent(AssignmentReleaseQuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AssignmentReleaseQuestionDO::getId));
    }

}