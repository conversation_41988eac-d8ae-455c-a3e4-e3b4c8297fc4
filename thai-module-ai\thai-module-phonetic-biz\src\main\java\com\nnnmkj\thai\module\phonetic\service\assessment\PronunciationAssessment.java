package com.nnnmkj.thai.module.phonetic.service.assessment;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.microsoft.cognitiveservices.speech.*;
import com.microsoft.cognitiveservices.speech.audio.*;
import com.nnnmkj.thai.framework.common.enums.LanguageEnum;
import com.nnnmkj.thai.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nnnmkj.thai.framework.common.exception.util.ServiceExceptionUtil;
import com.nnnmkj.thai.module.infra.api.file.FileApi;
import com.nnnmkj.thai.module.phonetic.controller.app.pronunciationaccessment.vo.AppPronunciationAssessmentReqVO;
import com.nnnmkj.thai.module.phonetic.controller.app.pronunciationaccessment.vo.AppPronunciationAssessmentRespVO;
import com.nnnmkj.thai.module.phonetic.enums.PronunciationAssessmentTypeEnum;
import com.nnnmkj.thai.module.phonetic.util.AudioUtils;
import com.nnnmkj.thai.module.phonetic.util.pronunciationassessment.SpeechProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 发音评估 Service 实现类
 */
@Service
@Slf4j
public class PronunciationAssessment implements PronunciationAssessmentService {

    @Resource
    private SpeechProperties speechProperties;
    
    @Resource
    private FileApi fileApi;

    @Override
    public AppPronunciationAssessmentRespVO assess(AppPronunciationAssessmentReqVO reqVO) throws Exception {
        PronunciationAssessmentTypeEnum typeEnum = PronunciationAssessmentTypeEnum.getByCode(reqVO.getType());
        LanguageEnum languageEnum = LanguageEnum.getByCode(reqVO.getLanguage());

        if (ObjectUtils.anyNull(typeEnum, languageEnum)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.BAD_REQUEST);
        }

        MultipartFile audioFile = reqVO.getAudioFile();
        String referenceText = reqVO.getReferenceText();

        log.info("开始发音评估，参考文本: {}, 音频文件名: {}, 音频大小: {}", referenceText, audioFile.getOriginalFilename(), audioFile.getSize());

        SpeechConfig config = null;
        AudioConfig audioInput = null;
        SpeechRecognizer recognizer = null;
        PronunciationAssessmentConfig pronunciationConfig = null;
        File tempFile = null;
        String tempFilePath;
        String audioUrl = null;
        boolean isPcmFile = false;

        try {
            // 配置 SpeechConfig
            config = SpeechConfig.fromEndpoint(new URI(speechProperties.getEndpoint()), speechProperties.getNextSubscriptionKey());
            config.setProperty(PropertyId.Speech_SegmentationSilenceTimeoutMs, "100");
            log.debug("已配置 SpeechConfig");

            // 保存上传的音频文件到临时目录
            tempFile = FileUtil.createTempFile();
            tempFilePath = tempFile.getAbsolutePath();
            audioFile.transferTo(tempFile);
            
            // 检查文件扩展名，区别处理WAV和PCM文件
            String fileName = audioFile.getOriginalFilename();
            if (fileName != null && fileName.toLowerCase().endsWith(".wav")) {
                // 对WAV文件使用原始方法
                audioInput = AudioConfig.fromWavFileInput(tempFilePath);
                log.debug("使用WAV文件输入: {}", tempFilePath);
            } else {
                // 对PCM文件使用流式处理
                audioInput = createPcmAudioInput(tempFilePath);
                isPcmFile = true;
                log.debug("使用PCM流式输入: {}", tempFilePath);
            }
            log.debug("已保存音频文件到临时目录: {}", tempFilePath);

            // 配置发音评估
            pronunciationConfig = new PronunciationAssessmentConfig(
                    referenceText,
                    PronunciationAssessmentGradingSystem.HundredMark,
                    PronunciationAssessmentGranularity.Phoneme,
                    true);
            pronunciationConfig.enableProsodyAssessment();
            log.debug("已配置发音评估");

            // 创建识别器
            recognizer = new SpeechRecognizer(config, languageEnum.getBCP47(), audioInput);
            log.debug("已创建识别器");

            // 应用发音评估配置
            pronunciationConfig.applyTo(recognizer);

            // 执行一次性识别
            log.debug("开始执行语音识别...");
            SpeechRecognitionResult result = recognizer.recognizeOnceAsync().get();
            log.debug("语音识别完成，结果原因: {}", result.getReason());

            // 上传音频文件（如果是PCM格式需要先转WAV）
            if (isPcmFile) {
                // PCM转WAV
                byte[] wavData = convertPcmToWav(tempFile);
                // 上传文件
                audioUrl = uploadAudioFile(wavData, getWavFileName(audioFile.getOriginalFilename()));
                log.info("PCM文件已转换为WAV并上传，URL: {}", audioUrl);
            } else {
                // 直接上传WAV文件
                byte[] audioBytes = FileUtil.readBytes(tempFile);
                audioUrl = uploadAudioFile(audioBytes, audioFile.getOriginalFilename());
                log.info("WAV文件已上传，URL: {}", audioUrl);
            }

            // 处理结果
            AppPronunciationAssessmentRespVO respVO;
            if (result.getReason() == ResultReason.RecognizedSpeech) {
                PronunciationAssessmentResult pronResult = PronunciationAssessmentResult.fromResult(result);
                respVO = AppPronunciationAssessmentRespVO.builder()
                        .success(true)
                        .accuracyScore(pronResult.getAccuracyScore())
                        .pronunciationScore(pronResult.getPronunciationScore())
                        .completenessScore(pronResult.getCompletenessScore())
                        .fluencyScore(pronResult.getFluencyScore())
                        .recognizedText(result.getText())
                        .audioUrl(audioUrl)
                        .build();
                log.info("发音评估成功，准确度: {}, 发音: {}, 完整度: {}, 流利度: {}, 识别文本: {}, 音频URL: {}",
                        pronResult.getAccuracyScore(), pronResult.getPronunciationScore(), pronResult.getCompletenessScore(), 
                        pronResult.getFluencyScore(), result.getText(), audioUrl);
            } else if (result.getReason() == ResultReason.NoMatch) {
                respVO = AppPronunciationAssessmentRespVO.builder()
                        .success(false)
                        .accuracyScore(0.0)
                        .pronunciationScore(0.0)
                        .completenessScore(0.0)
                        .fluencyScore(0.0)
                        .audioUrl(audioUrl)
                        .build();
                log.warn("发音评估失败，无法识别语音，音频URL: {}", audioUrl);
            } else {
                respVO = AppPronunciationAssessmentRespVO.builder()
                        .success(false)
                        .accuracyScore(0.0)
                        .pronunciationScore(0.0)
                        .completenessScore(0.0)
                        .fluencyScore(0.0)
                        .audioUrl(audioUrl)
                        .build();
                CancellationDetails cancellation = CancellationDetails.fromResult(result);
                String errorMessage = "识别取消: " + cancellation.getReason() + ", 错误详情: " + cancellation.getErrorDetails();
                log.error("发音评估失败，{}，音频URL: {}", errorMessage, audioUrl);
            }
            return respVO;
        } finally {
            // 清理资源
            if (pronunciationConfig != null) pronunciationConfig.close();
            if (recognizer != null) recognizer.close();
            if (audioInput != null) audioInput.close();
            if (config != null) config.close();
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                System.gc();
                FileUtil.del(tempFile);
            }
        }
        
    }
    
    /**
     * 上传音频文件到文件服务器
     *
     * @param audioData 音频数据
     * @param fileName 文件名
     * @return 文件URL
     */
    private String uploadAudioFile(byte[] audioData, String fileName) {
        try {
            // 生成存储路径: pronunciation/年月日/随机ID_文件名
            String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String path = "pronunciation/" + datePath + "/" + IdUtil.fastSimpleUUID() + "_" + fileName;
            
            // 调用文件API上传
            return fileApi.createFile(fileName, path, audioData);
        } catch (Exception e) {
            log.error("上传音频文件失败", e);
            return null;
        }
    }
    
    /**
     * 将PCM文件转换为WAV格式
     *
     * @param pcmFile PCM文件
     * @return WAV数据
     */
    private byte[] convertPcmToWav(File pcmFile) {
        // 使用标准录音参数：16kHz采样率，16位深度，单声道
        return AudioUtils.pcmFileToWav(pcmFile, 16000, (short)16, (short)1);
    }
    
    /**
     * 获取WAV格式的文件名（如果原来是PCM文件）
     *
     * @param originalFilename 原始文件名
     * @return WAV格式的文件名
     */
    private String getWavFileName(String originalFilename) {
        if (StringUtils.isBlank(originalFilename)) {
            return IdUtil.fastSimpleUUID() + ".wav";
        }
        
        int lastDotIndex = originalFilename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return originalFilename.substring(0, lastDotIndex) + ".wav";
        } else {
            return originalFilename + ".wav";
        }
    }
    
    /**
     * 创建PCM文件的音频输入配置
     * PCM要求：
     * - PCM格式（int-16，已签名）
     * - 单通道
     * - 每个样本16位，每秒8,000或16,000个样本（16kHz推荐）
     * - 双块对齐
     *
     * @param pcmFilePath PCM文件路径
     * @return 音频配置对象
     */
    private AudioConfig createPcmAudioInput(String pcmFilePath) throws IOException {
        final byte CHANNELS = 1;               // 单声道
        final byte BITS_PER_SAMPLE = 16;       // 16位深度
        final int SAMPLES_PER_SECOND = 16000;  // 16kHz采样率
        
        // 创建PCM格式的音频流格式
        AudioStreamFormat audioFormat = AudioStreamFormat.getWaveFormatPCM(SAMPLES_PER_SECOND, BITS_PER_SAMPLE, CHANNELS);
        log.debug("创建PCM音频格式: 采样率={}Hz, 位深={}, 声道={}", SAMPLES_PER_SECOND, BITS_PER_SAMPLE, CHANNELS);
        
        // 创建回调读取PCM数据
        PullAudioInputStreamCallback callback = new PullAudioInputStreamCallback() {
            private FileInputStream pcmStream;
            private byte[] readBuffer = new byte[32000]; // 确保足够大的缓冲区
            private int readOffset = 0;
            private int bytesAvailable = 0;
            
            @Override
            public int read(byte[] buffer) {
                try {
                    // 首次读取时打开文件流
                    if (pcmStream == null) {
                        pcmStream = new FileInputStream(pcmFilePath);
                        log.debug("已打开PCM文件流: {}", pcmFilePath);
                    }
                    
                    // 如果缓冲区中没有数据，从文件读取
                    if (readOffset >= bytesAvailable) {
                        readOffset = 0;
                        bytesAvailable = pcmStream.read(readBuffer);
                        
                        if (bytesAvailable <= 0) {
                            log.debug("PCM文件数据已读取完毕");
                            return 0; // 文件结束
                        }
                    }
                    
                    // 计算可复制的字节数
                    int toCopy = Math.min(buffer.length, bytesAvailable - readOffset);
                    
                    // 复制数据到目标缓冲区
                    System.arraycopy(readBuffer, readOffset, buffer, 0, toCopy);
                    readOffset += toCopy;
                    
                    return toCopy;
                } catch (IOException e) {
                    log.error("读取PCM文件失败", e);
                    return 0;
                }
            }
            
            @Override
            public void close() {
                try {
                    if (pcmStream != null) {
                        pcmStream.close();
                        pcmStream = null;
                        log.debug("PCM文件流已关闭");
                    }
                } catch (IOException e) {
                    log.error("关闭PCM文件流失败", e);
                }
            }
        };
        
        // 使用格式化参数创建拉取流
        PullAudioInputStream audioStream = AudioInputStream.createPullStream(callback, audioFormat);
        log.debug("已创建PCM拉取流");
        
        // 从流创建音频配置
        return AudioConfig.fromStreamInput(audioStream);
    }
}
