package com.nnnmkj.thai.module.quiz.controller.admin.questionBank.vo;

import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankKnowledgePoint.QuestionBankKnowledgePointDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankOption.QuestionBankOptionDO;
import com.nnnmkj.thai.module.quiz.dal.dataobject.questionBankTag.QuestionBankTagDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 题目新增/修改 Request VO")
@Data
public class QuestionBankSaveReqVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "19681")
    private Long id;

    @Schema(description = "AI出题ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15109")
    private Long aiQuestionId;

    @Schema(description = "题目类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "题目类型不能为空")
    private Integer questionType;

    @Schema(description = "创建方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer createMethod;

    @Schema(description = "是否公开", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "是否公开不能为空")
    private Boolean publicStatus;

    @Schema(description = "题干", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "题干不能为空")
    private String questionStem;

    @Schema(description = "参考答案", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "参考答案不能为空")
    private String answer;

    @Schema(description = "题目出处")
    private String source;

    @Schema(description = "试题解析")
    private String analysis;

    @Schema(description = "难易度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "难易度不能为空")
    private Integer difficulty;

    @Schema(description = "未通过原因", example = "不好")
    private String rejectReason;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "题目知识点列表")
    private List<QuestionBankKnowledgePointDO> questionBankKnowledgePoints;

    @Schema(description = "题目选项列表")
    private List<QuestionBankOptionDO> questionBankOptions;

    @Schema(description = "题目标签列表")
    private List<QuestionBankTagDO> questionBankTags;

}